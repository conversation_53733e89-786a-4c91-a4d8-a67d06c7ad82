"""
CNKI风格文档预览对话框
"""
import os
import urllib.parse
from pathlib import Path

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
        QPushButton, QSpinBox, QFrame, QMessageBox
    )
    from PyQt6.QtCore import Qt, QUrl
    from PyQt6.QtGui import QFont
    from PyQt6.QtWebEngineWidgets import QWebEngineView
except ImportError as e:
    raise e

from ..utils.logger import get_logger

logger = get_logger(__name__)


class CNKIPreviewDialog(QDialog):
    """CNKI风格文档预览对话框"""
    
    def __init__(self, file_path: str, page: int = 1, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.current_page = page
        self.total_pages = 100  # 默认值，实际应该从PDF获取
        
        self._init_ui()
        self._load_document()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            self.setWindowTitle("文档预览")
            self.setMinimumSize(900, 700)
            self.resize(1000, 800)
            
            # 设置窗口样式
            self.setStyleSheet("""
                QDialog {
                    background-color: #f5f5f5;
                }
            """)
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(10, 10, 10, 10)
            layout.setSpacing(10)
            
            # 创建顶部工具栏
            self._create_toolbar(layout)
            
            # 创建预览区域
            self._create_preview_area(layout)
            
            # 创建底部控制栏
            self._create_bottom_controls(layout)
            
        except Exception as e:
            logger.error(f"预览对话框界面初始化失败: {e}")
    
    def _create_toolbar(self, parent_layout):
        """创建顶部工具栏"""
        try:
            toolbar_frame = QFrame()
            toolbar_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 6px;
                    padding: 8px;
                }
            """)
            
            toolbar_layout = QHBoxLayout(toolbar_frame)
            
            # 文档标题
            file_name = Path(self.file_path).name if self.file_path else "未知文档"
            title_label = QLabel(f"📄 {file_name}")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 12pt;
                    font-weight: bold;
                    color: #1a5099;
                }
            """)
            toolbar_layout.addWidget(title_label)
            
            toolbar_layout.addStretch()
            
            # 外部打开按钮
            external_btn = QPushButton("📖 外部打开")
            external_btn.setStyleSheet("""
                QPushButton {
                    background-color: #1a5099;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 5px 10px;
                    font-size: 10pt;
                    margin-right: 5px;
                }
                QPushButton:hover {
                    background-color: #0d3d73;
                }
            """)
            external_btn.clicked.connect(self._open_external)
            toolbar_layout.addWidget(external_btn)

            # 关闭按钮
            close_btn = QPushButton("✕ 关闭")
            close_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    color: #333;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 5px 10px;
                    font-size: 10pt;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
            close_btn.clicked.connect(self.close)
            toolbar_layout.addWidget(close_btn)
            
            parent_layout.addWidget(toolbar_frame)
            
        except Exception as e:
            logger.error(f"工具栏创建失败: {e}")
    
    def _create_preview_area(self, parent_layout):
        """创建预览区域"""
        try:
            preview_frame = QFrame()
            preview_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 6px;
                }
            """)
            
            preview_layout = QVBoxLayout(preview_frame)
            preview_layout.setContentsMargins(0, 0, 0, 0)
            
            # PDF预览组件
            try:
                self.web_view = QWebEngineView()
                self.web_view.setStyleSheet("""
                    QWebEngineView {
                        border: none;
                        border-radius: 6px;
                    }
                """)
                preview_layout.addWidget(self.web_view)
                
            except Exception as e:
                logger.warning(f"WebEngine不可用，使用占位符: {e}")
                # 创建占位符
                placeholder = QLabel("PDF预览功能需要安装PyQt6-WebEngine")
                placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
                placeholder.setStyleSheet("""
                    QLabel {
                        font-size: 14pt;
                        color: #666;
                        background-color: #f9f9f9;
                        border: 2px dashed #ddd;
                        border-radius: 6px;
                        padding: 50px;
                    }
                """)
                preview_layout.addWidget(placeholder)
                self.web_view = None
            
            parent_layout.addWidget(preview_frame)
            
        except Exception as e:
            logger.error(f"预览区域创建失败: {e}")
    
    def _create_bottom_controls(self, parent_layout):
        """创建底部控制栏"""
        try:
            controls_frame = QFrame()
            controls_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 6px;
                    padding: 8px;
                }
            """)
            
            controls_layout = QHBoxLayout(controls_frame)
            
            # 页码信息
            self.page_info_label = QLabel(f"第 {self.current_page} 页")
            self.page_info_label.setStyleSheet("""
                QLabel {
                    font-size: 10pt;
                    color: #666;
                }
            """)
            controls_layout.addWidget(self.page_info_label)
            
            controls_layout.addStretch()
            
            # 页码导航控件
            nav_label = QLabel("跳转到页码:")
            nav_label.setStyleSheet("color: #333; font-size: 10pt;")
            controls_layout.addWidget(nav_label)
            
            self.page_spin = QSpinBox()
            self.page_spin.setRange(1, self.total_pages)
            self.page_spin.setValue(self.current_page)
            self.page_spin.setStyleSheet("""
                QSpinBox {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 10pt;
                    min-width: 60px;
                }
                QSpinBox:focus {
                    border-color: #1a5099;
                }
            """)
            self.page_spin.valueChanged.connect(self._on_page_changed)
            controls_layout.addWidget(self.page_spin)
            
            # 导航按钮
            prev_btn = QPushButton("◀ 上一页")
            prev_btn.clicked.connect(self._on_prev_page)
            
            next_btn = QPushButton("下一页 ▶")
            next_btn.clicked.connect(self._on_next_page)
            
            button_style = """
                QPushButton {
                    background-color: #1a5099;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 10pt;
                    margin: 0 2px;
                }
                QPushButton:hover {
                    background-color: #0d3d73;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                }
            """
            
            prev_btn.setStyleSheet(button_style)
            next_btn.setStyleSheet(button_style)
            
            controls_layout.addWidget(prev_btn)
            controls_layout.addWidget(next_btn)

            # 分隔符
            controls_layout.addWidget(QLabel("  |  "))

            # 外部打开按钮
            external_btn = QPushButton("📖 外部打开")
            external_btn.setStyleSheet(button_style)
            external_btn.clicked.connect(self._open_external)
            controls_layout.addWidget(external_btn)

            # 显示位置按钮
            location_btn = QPushButton("📁 显示位置")
            location_btn.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 10pt;
                    margin: 0 2px;
                }
                QPushButton:hover {
                    background-color: #545b62;
                }
            """)
            location_btn.clicked.connect(self._show_file_location)
            controls_layout.addWidget(location_btn)

            parent_layout.addWidget(controls_frame)
            
        except Exception as e:
            logger.error(f"底部控制栏创建失败: {e}")
    
    def _load_document(self):
        """加载文档"""
        try:
            if not self.web_view or not self.file_path:
                return
            
            if not os.path.exists(self.file_path):
                QMessageBox.warning(self, "文件不存在", f"无法找到文件：{self.file_path}")
                return
            
            # 检查是否为PDF文件
            if not self.file_path.lower().endswith('.pdf'):
                QMessageBox.warning(self, "文件格式不支持", "当前只支持PDF文件预览")
                return
            
            # 使用PDF.js加载PDF
            self._load_pdf_with_pdfjs()
            
        except Exception as e:
            logger.error(f"文档加载失败: {e}")
            QMessageBox.critical(self, "加载失败", f"文档加载失败：{str(e)}")
    
    def _load_pdf_with_pdfjs(self):
        """使用多种方式加载PDF"""
        try:
            # 方法1: 直接使用QWebEngineView加载PDF文件
            if self._try_direct_pdf_load():
                return

            # 方法2: 检查PDF.js是否存在
            if self._try_pdfjs_load():
                return

            # 方法3: 使用简单预览
            logger.warning("所有PDF加载方法失败，使用简单预览")
            self._show_simple_preview()

        except Exception as e:
            logger.error(f"PDF加载失败: {e}")
            self._show_simple_preview()

    def _try_direct_pdf_load(self):
        """尝试直接加载PDF"""
        try:
            # 现代浏览器引擎通常支持直接显示PDF
            pdf_url = QUrl.fromLocalFile(str(Path(self.file_path).absolute()))
            logger.info(f"尝试直接加载PDF: {pdf_url.toString()}")
            self.web_view.load(pdf_url)
            return True

        except Exception as e:
            logger.warning(f"直接PDF加载失败: {e}")
            return False

    def _try_pdfjs_load(self):
        """尝试使用PDF.js加载"""
        try:
            # 检查PDF.js是否存在
            pdfjs_path = Path("pdfjs/web/viewer.html")
            if not pdfjs_path.exists():
                # 尝试相对于项目根目录的路径
                project_root = Path(__file__).parent.parent.parent
                pdfjs_path = project_root / "pdfjs" / "web" / "viewer.html"

            if not pdfjs_path.exists():
                logger.warning("PDF.js未找到")
                return False

            # 构建PDF.js URL
            pdfjs_url = f"file:///{pdfjs_path.absolute().as_posix()}"
            pdf_url = f"file:///{Path(self.file_path).absolute().as_posix()}"

            # 编码URL
            encoded_pdf_url = urllib.parse.quote(pdf_url, safe=':/')

            # 构建完整URL
            full_url = f"{pdfjs_url}?file={encoded_pdf_url}#page={self.current_page}"

            logger.info(f"使用PDF.js加载: {full_url}")
            self.web_view.load(QUrl(full_url))
            return True

        except Exception as e:
            logger.warning(f"PDF.js加载失败: {e}")
            return False
    
    def _show_simple_preview(self):
        """显示简单预览"""
        try:
            if not self.web_view:
                return

            file_name = Path(self.file_path).name
            file_size = self._get_file_size()

            # 创建简单的HTML预览页面
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>文档预览</title>
                <style>
                    body {{
                        font-family: "Microsoft YaHei", sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                        text-align: center;
                    }}
                    .container {{
                        background-color: white;
                        border-radius: 8px;
                        padding: 40px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        max-width: 600px;
                        margin: 50px auto;
                    }}
                    .icon {{
                        font-size: 48px;
                        color: #1a5099;
                        margin-bottom: 20px;
                    }}
                    .title {{
                        font-size: 18px;
                        color: #333;
                        margin-bottom: 10px;
                        font-weight: bold;
                    }}
                    .info {{
                        color: #666;
                        margin-bottom: 15px;
                        padding: 8px;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                    }}
                    .note {{
                        background-color: #f0f8ff;
                        border-left: 4px solid #1a5099;
                        padding: 15px;
                        margin-top: 20px;
                        text-align: left;
                    }}
                    .buttons {{
                        margin-top: 20px;
                    }}
                    .btn {{
                        background-color: #1a5099;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        padding: 10px 20px;
                        margin: 5px;
                        cursor: pointer;
                        font-size: 14px;
                        text-decoration: none;
                        display: inline-block;
                    }}
                    .btn:hover {{
                        background-color: #0d3d73;
                    }}
                    .btn-secondary {{
                        background-color: #6c757d;
                    }}
                    .btn-secondary:hover {{
                        background-color: #545b62;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="icon">📄</div>
                    <div class="title">文档预览</div>
                    <div class="info"><strong>文件名:</strong> {file_name}</div>
                    <div class="info"><strong>文件大小:</strong> {file_size}</div>
                    <div class="info"><strong>当前页:</strong> {self.current_page}</div>

                    <div class="buttons">
                        <button class="btn" onclick="openExternal()">📖 用外部程序打开</button>
                        <button class="btn btn-secondary" onclick="showPath()">📁 显示文件位置</button>
                    </div>

                    <div class="note">
                        <strong>💡 提示：</strong><br>
                        • 点击"用外部程序打开"可以使用系统默认的PDF阅读器查看文档<br>
                        • 点击"显示文件位置"可以在文件管理器中定位文件<br>
                        • 如需在线预览，请确保已安装PyQt6-WebEngine组件
                    </div>
                </div>

                <script>
                    function openExternal() {{
                        // 这个功能需要通过Qt信号实现
                        console.log('Open external requested');
                    }}

                    function showPath() {{
                        // 这个功能需要通过Qt信号实现
                        console.log('Show path requested');
                    }}
                </script>
            </body>
            </html>
            """

            self.web_view.setHtml(html_content)

        except Exception as e:
            logger.error(f"简单预览显示失败: {e}")

    def _get_file_size(self):
        """获取文件大小"""
        try:
            size_bytes = os.path.getsize(self.file_path)
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
        except:
            return "未知"
    
    def _on_page_changed(self, page: int):
        """页码改变事件"""
        try:
            if page != self.current_page:
                self.current_page = page
                self._update_page_info()
                self._load_document()
                
        except Exception as e:
            logger.error(f"页码改变处理失败: {e}")
    
    def _on_prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.page_spin.setValue(self.current_page)
    
    def _on_next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.page_spin.setValue(self.current_page)
    
    def _update_page_info(self):
        """更新页码信息"""
        try:
            self.page_info_label.setText(f"第 {self.current_page} 页 / 共 {self.total_pages} 页")
            
        except Exception as e:
            logger.error(f"页码信息更新失败: {e}")
    
    def goto_page(self, page: int):
        """跳转到指定页码"""
        try:
            if 1 <= page <= self.total_pages:
                self.current_page = page
                self.page_spin.setValue(page)
                self._update_page_info()
                self._load_document()

        except Exception as e:
            logger.error(f"页码跳转失败: {e}")

    def _open_external(self):
        """使用外部程序打开文档"""
        try:
            import subprocess
            import platform

            if not os.path.exists(self.file_path):
                QMessageBox.warning(self, "文件不存在", f"无法找到文件：{self.file_path}")
                return

            system = platform.system()

            if system == "Windows":
                # Windows: 使用默认程序打开
                os.startfile(self.file_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", self.file_path])
            else:  # Linux
                subprocess.run(["xdg-open", self.file_path])

            logger.info(f"使用外部程序打开文档: {self.file_path}")

        except Exception as e:
            logger.error(f"外部程序打开失败: {e}")
            QMessageBox.critical(self, "打开失败", f"无法使用外部程序打开文档：{str(e)}")

    def _show_file_location(self):
        """在文件管理器中显示文件位置"""
        try:
            import subprocess
            import platform

            if not os.path.exists(self.file_path):
                QMessageBox.warning(self, "文件不存在", f"无法找到文件：{self.file_path}")
                return

            system = platform.system()
            file_dir = os.path.dirname(self.file_path)

            if system == "Windows":
                # Windows: 在资源管理器中选中文件
                subprocess.run(["explorer", "/select,", self.file_path])
            elif system == "Darwin":  # macOS
                subprocess.run(["open", "-R", self.file_path])
            else:  # Linux
                subprocess.run(["xdg-open", file_dir])

            logger.info(f"在文件管理器中显示: {self.file_path}")

        except Exception as e:
            logger.error(f"显示文件位置失败: {e}")
            QMessageBox.critical(self, "操作失败", f"无法显示文件位置：{str(e)}")
