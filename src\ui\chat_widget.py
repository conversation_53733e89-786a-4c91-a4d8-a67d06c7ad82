"""
AI问答界面组件
"""
import time
from typing import List, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
        QPushButton, QLabel, QScrollArea, QFrame,
        QSplitter, QListWidget, QListWidgetItem
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt6.QtGui import QFont, QTextCursor, QColor
except ImportError as e:
    raise e

from ..utils.logger import get_logger
from ..core.search_system import get_search_system

logger = get_logger(__name__)


class AIQueryThread(QThread):
    """AI查询线程"""
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query: str):
        super().__init__()
        self.query = query
    
    def run(self):
        """执行AI查询"""
        try:
            search_system = get_search_system()
            response = search_system.ai_question(self.query)
            self.result_ready.emit(response.to_dict())
            
        except Exception as e:
            logger.error(f"AI查询失败: {e}")
            self.error_occurred.emit(str(e))


class SourceItem(QFrame):
    """来源项组件"""
    
    def __init__(self, source_data: dict, parent=None):
        super().__init__(parent)
        self.source_data = source_data
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            self.setFrameStyle(QFrame.Shape.Box)
            self.setStyleSheet("""
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #f9f9f9;
                    margin: 2px;
                    padding: 5px;
                }
                QFrame:hover {
                    background-color: #e9e9e9;
                    border-color: #007acc;
                }
            """)
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(8, 8, 8, 8)
            
            # 文件名
            file_name = self.source_data.get('file_name', '未知文件')
            file_label = QLabel(f"📄 {file_name}")
            file_label.setFont(QFont("", 9, QFont.Weight.Bold))
            layout.addWidget(file_label)
            
            # 部门信息
            department = self.source_data.get('department', '未知部门')
            dept_label = QLabel(f"🏢 {department}")
            dept_label.setFont(QFont("", 8))
            dept_label.setStyleSheet("color: #666;")
            layout.addWidget(dept_label)
            
            # 内容预览
            content = self.source_data.get('content', '')
            preview = content[:100] + "..." if len(content) > 100 else content
            content_label = QLabel(preview)
            content_label.setFont(QFont("", 8))
            content_label.setWordWrap(True)
            content_label.setStyleSheet("color: #333; margin-top: 5px;")
            layout.addWidget(content_label)
            
            # 相似度分数
            score = self.source_data.get('score', 0)
            score_label = QLabel(f"相似度: {score:.3f}")
            score_label.setFont(QFont("", 7))
            score_label.setStyleSheet("color: #007acc;")
            layout.addWidget(score_label)
            
        except Exception as e:
            logger.error(f"来源项界面初始化失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                # 发送预览请求信号
                file_path = self.source_data.get('source_file', '')
                page_number = self.source_data.get('page_number', 1)
                
                if file_path:
                    # 通过父组件发送信号
                    parent_widget = self.parent()
                    while parent_widget and not hasattr(parent_widget, 'preview_requested'):
                        parent_widget = parent_widget.parent()
                    
                    if parent_widget and hasattr(parent_widget, 'preview_requested'):
                        parent_widget.preview_requested.emit(file_path, page_number)
            
        except Exception as e:
            logger.error(f"来源项点击处理失败: {e}")


class ChatWidget(QWidget):
    """AI问答组件"""
    preview_requested = pyqtSignal(str, int)  # 文件路径, 页码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_system = None
        self.query_thread = None
        
        self._init_ui()
        self._init_search_system()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            layout = QVBoxLayout(self)
            
            # 创建分割器
            splitter = QSplitter(Qt.Orientation.Vertical)
            layout.addWidget(splitter)
            
            # 上半部分：对话区域
            self._create_chat_area(splitter)
            
            # 下半部分：来源区域
            self._create_sources_area(splitter)
            
            # 设置分割器比例
            splitter.setSizes([400, 200])
            
            # 输入区域
            self._create_input_area(layout)
            
        except Exception as e:
            logger.error(f"聊天界面初始化失败: {e}")
    
    def _create_chat_area(self, parent):
        """创建对话区域"""
        try:
            # 对话显示区域
            self.chat_display = QTextEdit()
            self.chat_display.setReadOnly(True)
            self.chat_display.setFont(QFont("", 10))
            self.chat_display.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: white;
                    padding: 10px;
                }
            """)
            
            # 添加欢迎消息
            welcome_msg = """
            <div style='color: #666; font-style: italic; margin-bottom: 10px;'>
            欢迎使用公司制度AI问答！<br>
            您可以询问关于公司制度、规定、流程等相关问题。<br>
            </div>
            """
            self.chat_display.setHtml(welcome_msg)
            
            parent.addWidget(self.chat_display)
            
        except Exception as e:
            logger.error(f"对话区域创建失败: {e}")
    
    def _create_sources_area(self, parent):
        """创建来源区域"""
        try:
            # 来源标题
            sources_frame = QFrame()
            sources_layout = QVBoxLayout(sources_frame)
            
            sources_title = QLabel("📚 相关来源")
            sources_title.setFont(QFont("", 10, QFont.Weight.Bold))
            sources_title.setStyleSheet("color: #333; margin-bottom: 5px;")
            sources_layout.addWidget(sources_title)
            
            # 来源滚动区域
            self.sources_scroll = QScrollArea()
            self.sources_scroll.setWidgetResizable(True)
            self.sources_scroll.setStyleSheet("""
                QScrollArea {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: white;
                }
            """)
            
            # 来源容器
            self.sources_container = QWidget()
            self.sources_layout = QVBoxLayout(self.sources_container)
            self.sources_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
            
            self.sources_scroll.setWidget(self.sources_container)
            sources_layout.addWidget(self.sources_scroll)
            
            parent.addWidget(sources_frame)
            
        except Exception as e:
            logger.error(f"来源区域创建失败: {e}")
    
    def _create_input_area(self, parent_layout):
        """创建输入区域"""
        try:
            input_frame = QFrame()
            input_layout = QVBoxLayout(input_frame)
            
            # 输入框
            self.input_text = QTextEdit()
            self.input_text.setMaximumHeight(80)
            self.input_text.setPlaceholderText("请输入您的问题...")
            self.input_text.setFont(QFont("", 10))
            self.input_text.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 8px;
                }
            """)
            input_layout.addWidget(self.input_text)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            
            # 发送按钮
            self.send_button = QPushButton("发送")
            self.send_button.setFont(QFont("", 10))
            self.send_button.setStyleSheet("""
                QPushButton {
                    background-color: #007acc;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #005a9e;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                }
            """)
            self.send_button.clicked.connect(self._on_send_clicked)
            
            # 清空按钮
            clear_button = QPushButton("清空")
            clear_button.setFont(QFont("", 10))
            clear_button.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    color: #333;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
            clear_button.clicked.connect(self._on_clear_clicked)
            
            button_layout.addStretch()
            button_layout.addWidget(clear_button)
            button_layout.addWidget(self.send_button)
            
            input_layout.addLayout(button_layout)
            parent_layout.addWidget(input_frame)
            
        except Exception as e:
            logger.error(f"输入区域创建失败: {e}")
    
    def _init_search_system(self):
        """初始化搜索系统"""
        try:
            self.search_system = get_search_system()
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
    
    def _on_send_clicked(self):
        """发送按钮点击事件"""
        try:
            query = self.input_text.toPlainText().strip()
            if not query:
                return
            
            # 禁用发送按钮
            self.send_button.setEnabled(False)
            self.send_button.setText("处理中...")
            
            # 添加用户问题到对话区域
            self._add_user_message(query)
            
            # 清空输入框
            self.input_text.clear()
            
            # 启动AI查询线程
            self.query_thread = AIQueryThread(query)
            self.query_thread.result_ready.connect(self._on_result_ready)
            self.query_thread.error_occurred.connect(self._on_error_occurred)
            self.query_thread.start()
            
        except Exception as e:
            logger.error(f"发送处理失败: {e}")
            self._reset_send_button()
    
    def _on_clear_clicked(self):
        """清空按钮点击事件"""
        try:
            self.input_text.clear()
            self.chat_display.clear()
            self._clear_sources()
            
            # 重新添加欢迎消息
            welcome_msg = """
            <div style='color: #666; font-style: italic;'>
            对话已清空，请输入新的问题。
            </div>
            """
            self.chat_display.setHtml(welcome_msg)
            
        except Exception as e:
            logger.error(f"清空处理失败: {e}")
    
    def _add_user_message(self, message: str):
        """添加用户消息"""
        try:
            cursor = self.chat_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            
            user_html = f"""
            <div style='margin: 10px 0; text-align: right;'>
                <div style='background-color: #007acc; color: white; padding: 10px; 
                           border-radius: 10px; display: inline-block; max-width: 70%;'>
                    {message}
                </div>
            </div>
            """
            cursor.insertHtml(user_html)
            self.chat_display.setTextCursor(cursor)
            
        except Exception as e:
            logger.error(f"添加用户消息失败: {e}")
    
    def _add_ai_message(self, message: str):
        """添加AI回复"""
        try:
            cursor = self.chat_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            
            ai_html = f"""
            <div style='margin: 10px 0; text-align: left;'>
                <div style='background-color: #f0f0f0; color: #333; padding: 10px; 
                           border-radius: 10px; display: inline-block; max-width: 70%;'>
                    🤖 {message}
                </div>
            </div>
            """
            cursor.insertHtml(ai_html)
            self.chat_display.setTextCursor(cursor)
            
        except Exception as e:
            logger.error(f"添加AI消息失败: {e}")
    
    def _on_result_ready(self, result: dict):
        """处理查询结果"""
        try:
            answer = result.get('answer', '抱歉，无法生成回答。')
            sources = result.get('sources', [])
            
            # 添加AI回复
            self._add_ai_message(answer)
            
            # 显示来源
            self._display_sources(sources)
            
            # 重置发送按钮
            self._reset_send_button()
            
        except Exception as e:
            logger.error(f"结果处理失败: {e}")
            self._reset_send_button()
    
    def _on_error_occurred(self, error: str):
        """处理错误"""
        try:
            error_msg = f"抱歉，处理您的问题时出现错误：{error}"
            self._add_ai_message(error_msg)
            self._reset_send_button()
            
        except Exception as e:
            logger.error(f"错误处理失败: {e}")
            self._reset_send_button()
    
    def _display_sources(self, sources: List[dict]):
        """显示来源"""
        try:
            # 清空现有来源
            self._clear_sources()
            
            if not sources:
                no_sources_label = QLabel("暂无相关来源")
                no_sources_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
                self.sources_layout.addWidget(no_sources_label)
                return
            
            # 添加来源项
            for source in sources:
                source_item = SourceItem(source)
                self.sources_layout.addWidget(source_item)
            
        except Exception as e:
            logger.error(f"来源显示失败: {e}")
    
    def _clear_sources(self):
        """清空来源"""
        try:
            while self.sources_layout.count():
                child = self.sources_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
                    
        except Exception as e:
            logger.error(f"清空来源失败: {e}")
    
    def _reset_send_button(self):
        """重置发送按钮"""
        try:
            self.send_button.setEnabled(True)
            self.send_button.setText("发送")
            
        except Exception as e:
            logger.error(f"重置发送按钮失败: {e}")
