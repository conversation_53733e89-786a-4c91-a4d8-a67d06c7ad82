"""
辅助函数模块
"""
import os
import sys
import hashlib
import platform
import time
import functools
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Callable, Type

try:
    import torch
except ImportError:
    torch = None

try:
    import psutil
except ImportError:
    psutil = None

# 延迟导入logger，避免循环依赖
def _get_logger():
    """延迟获取logger"""
    try:
        from .logger import get_logger
        return get_logger(__name__)
    except Exception:
        # 如果logger不可用，返回一个简单的打印函数
        class SimpleLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
        return SimpleLogger()


def get_system_info() -> Dict[str, Any]:
    """获取系统信息

    Returns:
        Dict[str, Any]: 包含系统平台、内存、CPU、GPU等信息的字典

    Example:
        >>> info = get_system_info()
        >>> print(f"总内存: {info['memory_total_gb']}GB")
    """
    info = {
        'platform': platform.platform(),
        'system': platform.system(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version(),
    }

    # 安全地获取内存信息
    if psutil is not None:
        try:
            info['memory_total_gb'] = round(psutil.virtual_memory().total / (1024**3), 2)
            info['memory_available_gb'] = round(psutil.virtual_memory().available / (1024**3), 2)
            info['cpu_count'] = psutil.cpu_count()
        except Exception:
            info['memory_total_gb'] = 0
            info['memory_available_gb'] = 0
            info['cpu_count'] = 0
    else:
        info['memory_total_gb'] = 0
        info['memory_available_gb'] = 0
        info['cpu_count'] = 0

    # 安全地获取GPU信息
    if torch is not None:
        try:
            info['gpu_available'] = torch.cuda.is_available()
            info['gpu_count'] = torch.cuda.device_count() if torch.cuda.is_available() else 0

            if info['gpu_available']:
                info['gpu_name'] = torch.cuda.get_device_name(0)
                info['gpu_memory_gb'] = round(
                    torch.cuda.get_device_properties(0).total_memory / (1024**3), 2
                )
        except Exception:
            info['gpu_available'] = False
            info['gpu_count'] = 0
    else:
        info['gpu_available'] = False
        info['gpu_count'] = 0

    return info


def check_system_requirements() -> Dict[str, bool]:
    """检查系统要求"""
    info = get_system_info()
    requirements = {
        'memory_sufficient': info.get('memory_total_gb', 0) >= 8,
        'python_version_ok': sys.version_info >= (3, 8),
        'windows_system': info.get('system', '') == 'Windows',
        'torch_available': torch is not None,
        'psutil_available': psutil is not None
    }

    _get_logger().info(f"系统检查结果: {requirements}")
    return requirements


def get_optimal_device() -> str:
    """获取最优设备"""
    if torch is not None:
        try:
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                if gpu_memory >= 6:  # 至少6GB显存
                    _get_logger().info(f"使用GPU设备，显存: {gpu_memory:.1f}GB")
                    return "cuda"
        except Exception as e:
            _get_logger().warning(f"GPU检查失败: {e}")

    _get_logger().info("使用CPU设备")
    return "cpu"


def calculate_file_hash(file_path: Union[str, Path]) -> str:
    """计算文件哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def ensure_dir(path: Union[str, Path]) -> Path:
    """确保目录存在"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_file_size_mb(file_path: Union[str, Path]) -> float:
    """获取文件大小(MB)"""
    return os.path.getsize(file_path) / (1024 * 1024)


def scan_documents(docs_dir: Union[str, Path], 
                  extensions: Optional[List[str]] = None) -> List[Path]:
    """扫描文档文件"""
    if extensions is None:
        extensions = ['.pdf', '.docx', '.doc', '.xlsx', '.xls']
    
    docs_dir = Path(docs_dir)
    documents = []
    
    for ext in extensions:
        documents.extend(docs_dir.rglob(f"*{ext}"))
    
    _get_logger().info(f"扫描到 {len(documents)} 个文档文件")
    return documents


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截断文本"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def clean_text(text: str) -> str:
    """清理文本，移除潜在的恶意内容"""
    if not isinstance(text, str):
        return ""

    # 限制文本长度，防止DoS攻击
    if len(text) > 100000:  # 100KB限制
        text = text[:100000]

    # 移除多余的空白字符
    text = ' '.join(text.split())

    # 移除控制字符和不可见字符
    import re
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

    # 移除特殊字符（保留中文、英文、数字、常用标点）
    text = re.sub(r'[^\u4e00-\u9fff\w\s.,;:!?()（）【】""''、。，；：！？\-]', '', text)

    # 移除潜在的脚本标签
    text = re.sub(r'<[^>]*>', '', text)

    return text.strip()


def validate_query(query: str) -> str:
    """验证和清理查询字符串"""
    # 延迟导入避免循环依赖
    try:
        from .exceptions import ValidationError
    except ImportError:
        # 如果无法导入，使用标准ValueError
        ValidationError = ValueError

    if not isinstance(query, str):
        raise ValidationError("查询必须是字符串类型", field_name="query", field_value=str(type(query)))

    query = query.strip()

    if not query:
        raise ValidationError("查询不能为空", field_name="query", field_value=query)

    if len(query) > 1000:
        raise ValidationError("查询长度不能超过1000个字符", field_name="query", field_value=f"长度: {len(query)}")

    # 检查字符编码
    try:
        query.encode('utf-8')
    except UnicodeEncodeError:
        raise ValidationError("查询包含无效字符", field_name="query", field_value=query)

    # 检查是否包含危险模式
    dangerous_patterns = [
        # SQL注入模式
        r'\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b',
        r'[\'";]',
        r'--',
        r'/\*.*\*/',
        r'\bOR\s+\d+\s*=\s*\d+',
        r'\bAND\s+\d+\s*=\s*\d+',
        # XSS模式
        r'<\s*script[^>]*>',
        r'javascript\s*:',
        r'on\w+\s*=',
        # 命令注入模式
        r'[;&|`$]',
        r'\b(eval|exec|system|shell_exec)\s*\(',
        # 路径遍历模式
        r'\.\./|\.\.\\'
    ]

    import re
    for pattern in dangerous_patterns:
        if re.search(pattern, query, re.IGNORECASE):
            raise ValidationError("查询包含不安全的内容", field_name="query", field_value=query)

    # 检查连续特殊字符
    if re.search(r'[^\w\s\u4e00-\u9fff]{3,}', query):
        raise ValidationError("查询包含过多连续特殊字符", field_name="query", field_value=query)

    # 检查重复字符（可能的DoS攻击）
    if re.search(r'(.)\1{50,}', query):
        raise ValidationError("查询包含过多重复字符", field_name="query", field_value=query)

    return clean_text(query)


def extract_department_from_filename(filename: str) -> str:
    """从文件名提取部门信息"""
    # 根据文件路径或文件名提取部门
    department_mapping = {
        '人组部': '人组部',
        '安监部': '安监部',
        '审计部': '审计部',
        '总经办': '总经办',
        '技术部': '设备部',
        '生产部': '生产部',
        '财务部': '财务部',
        '党办': '党办'
    }
    
    for key, value in department_mapping.items():
        if key in filename:
            return value
    
    return '未知部门'


def validate_model_path(model_path: Union[str, Path]) -> bool:
    """验证模型路径"""
    model_path = Path(model_path)
    
    # 检查目录是否存在
    if not model_path.exists():
        return False
    
    # 检查是否包含必要的模型文件
    required_files = ['config.json', 'pytorch_model.bin']
    for file in required_files:
        if not (model_path / file).exists():
            # 尝试查找其他可能的模型文件
            if not any(model_path.glob('*.bin')) and not any(model_path.glob('*.safetensors')):
                return False
    
    return True


def get_memory_usage() -> Dict[str, float]:
    """获取内存使用情况"""
    if psutil is None:
        return {
            'rss_mb': 0.0,
            'vms_mb': 0.0,
            'percent': 0.0
        }

    try:
        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            'rss_mb': memory_info.rss / (1024 * 1024),  # 物理内存
            'vms_mb': memory_info.vms / (1024 * 1024),  # 虚拟内存
            'percent': process.memory_percent()  # 内存使用百分比
        }
    except Exception:
        return {
            'rss_mb': 0.0,
            'vms_mb': 0.0,
            'percent': 0.0
        }


def check_disk_space(path: Union[str, Path], required_gb: float = 5.0) -> bool:
    """检查磁盘空间"""
    if psutil is None:
        return True  # 无法检查时假设有足够空间

    try:
        path = Path(path)
        if not path.exists():
            path = path.parent

        free_space = psutil.disk_usage(str(path)).free / (1024**3)
        return free_space >= required_gb
    except Exception:
        return True  # 检查失败时假设有足够空间


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
    
    def update(self, increment: int = 1):
        """更新进度"""
        self.current += increment
        percentage = (self.current / self.total) * 100
        _get_logger().info(f"{self.description}: {self.current}/{self.total} ({percentage:.1f}%)")

    def finish(self):
        """完成"""
        _get_logger().info(f"{self.description}: 完成 ({self.total}/{self.total})")


def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    import re

    if not isinstance(filename, str):
        raise ValueError("文件名必须是字符串")

    # 移除路径分隔符，防止路径遍历
    filename = os.path.basename(filename)

    # 检查空文件名
    if not filename.strip():
        return "unnamed_file"

    # 移除或替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

    # 移除控制字符和不可见字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)

    # 移除Unicode控制字符
    filename = re.sub(r'[\u0000-\u001f\u007f-\u009f\u2000-\u200f\u2028-\u202f]', '', filename)

    # 处理特殊文件名（Windows保留名）
    reserved_names = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }

    name_without_ext = os.path.splitext(filename)[0].upper()
    if name_without_ext in reserved_names:
        filename = f"safe_{filename}"

    # 移除开头和结尾的点和空格
    filename = filename.strip('. ')

    # 确保文件名不为空
    if not filename:
        filename = "unnamed_file"

    # 限制长度（考虑文件系统限制）
    max_length = 200  # 保守的长度限制
    if len(filename) > max_length:
        name, ext = os.path.splitext(filename)
        available_length = max_length - len(ext) - 10  # 预留空间
        if available_length > 0:
            filename = name[:available_length] + "_truncated" + ext
        else:
            filename = "truncated_file" + (ext if len(ext) < 10 else ".txt")

    # 最终验证
    if not filename or filename in ['.', '..']:
        filename = "safe_file"

    return filename


def safe_path_join(base_path: Union[str, Path], *paths: str) -> Path:
    """安全的路径拼接，防止路径遍历攻击"""
    base_path = Path(base_path).resolve()
    original_base = base_path

    for path in paths:
        if not isinstance(path, str):
            raise ValueError("路径组件必须是字符串")

        # 检查危险的路径模式
        dangerous_patterns = ['..', '~', '\\', '//', '/./', '/../']
        for pattern in dangerous_patterns:
            if pattern in path:
                raise ValueError(f"检测到危险路径模式: {pattern}")

        # 检查绝对路径
        if os.path.isabs(path):
            raise ValueError("不允许绝对路径")

        # 规范化路径组件
        path = os.path.normpath(path)

        # 只允许文件名，不允许目录分隔符
        if os.sep in path or (os.altsep and os.altsep in path):
            raise ValueError("路径组件不能包含目录分隔符")

        # 检查保留名称（Windows）
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        if path.upper() in reserved_names:
            raise ValueError(f"不允许使用保留名称: {path}")

        base_path = base_path / path

    # 确保最终路径在原始基础路径下
    try:
        final_path = base_path.resolve()
        final_path.relative_to(original_base)
    except ValueError:
        raise ValueError("路径遍历攻击检测到")

    return base_path


def create_secure_temp_file(suffix: str = '', prefix: str = 'localqa_',
                           dir: Optional[Union[str, Path]] = None) -> Path:
    """创建安全的临时文件"""
    import tempfile
    import stat

    # 创建临时文件
    fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)

    try:
        # 设置安全的文件权限（仅所有者可读写）
        os.chmod(temp_path, stat.S_IRUSR | stat.S_IWUSR)

        # 关闭文件描述符
        os.close(fd)

        return Path(temp_path)
    except Exception:
        # 清理失败的临时文件
        try:
            os.close(fd)
            os.unlink(temp_path)
        except Exception:
            pass
        raise


def secure_delete_file(file_path: Union[str, Path]) -> bool:
    """安全删除文件（覆盖后删除）"""
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return True

        # 获取文件大小
        file_size = file_path.stat().st_size

        # 用随机数据覆盖文件内容
        with open(file_path, 'r+b') as f:
            import random
            for _ in range(3):  # 覆盖3次
                f.seek(0)
                f.write(bytes([random.randint(0, 255) for _ in range(file_size)]))
                f.flush()
                os.fsync(f.fileno())

        # 删除文件
        file_path.unlink()
        return True

    except Exception as e:
        _get_logger().error(f"安全删除文件失败 {file_path}: {e}")
        return False


def retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    on_retry: Optional[Callable] = None
):
    """重试装饰器

    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
        on_retry: 重试时的回调函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e

                    if attempt == max_retries:
                        # 最后一次尝试失败
                        _get_logger().error(
                            f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}"
                        )
                        break

                    # 计算延迟时间
                    current_delay = delay * (backoff_factor ** attempt)

                    _get_logger().warning(
                        f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，"
                        f"{current_delay:.1f}秒后重试"
                    )

                    # 执行重试回调
                    if on_retry:
                        try:
                            on_retry(attempt, e)
                        except Exception as callback_error:
                            _get_logger().error(f"重试回调执行失败: {callback_error}")

                    time.sleep(current_delay)

            # 抛出最后的异常
            raise last_exception

        return wrapper
    return decorator


def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    expected_exception: Type[Exception] = Exception
):
    """断路器装饰器

    Args:
        failure_threshold: 失败阈值
        recovery_timeout: 恢复超时时间（秒）
        expected_exception: 预期的异常类型
    """
    def decorator(func: Callable) -> Callable:
        func._circuit_breaker_state = {
            'failure_count': 0,
            'last_failure_time': None,
            'state': 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        }

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            state = func._circuit_breaker_state
            current_time = time.time()

            # 检查断路器状态
            if state['state'] == 'OPEN':
                if current_time - state['last_failure_time'] > recovery_timeout:
                    state['state'] = 'HALF_OPEN'
                    _get_logger().info(f"断路器 {func.__name__} 进入半开状态")
                else:
                    raise Exception(f"断路器 {func.__name__} 处于开启状态，拒绝请求")

            try:
                result = func(*args, **kwargs)

                # 成功执行，重置失败计数
                if state['failure_count'] > 0:
                    state['failure_count'] = 0
                    state['state'] = 'CLOSED'
                    _get_logger().info(f"断路器 {func.__name__} 恢复正常")

                return result

            except expected_exception as e:
                state['failure_count'] += 1
                state['last_failure_time'] = current_time

                if state['failure_count'] >= failure_threshold:
                    state['state'] = 'OPEN'
                    _get_logger().error(
                        f"断路器 {func.__name__} 开启，失败次数: {state['failure_count']}"
                    )

                raise e

        return wrapper
    return decorator


def resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径，兼容PyInstaller打包和开发环境。
    """
    import sys, os
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    # 若为打包环境，使用可执行文件所在目录
    if getattr(sys, 'frozen', False):
        return os.path.join(os.path.dirname(sys.executable), relative_path)
    return os.path.join(os.path.abspath("."), relative_path)
