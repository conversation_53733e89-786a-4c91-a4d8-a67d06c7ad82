"""
依赖检查模块
检查系统依赖和Python包的安装情况
"""
import sys
import importlib
import subprocess
from typing import Dict, List, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass

from .logger import get_logger
from .exceptions import ConfigurationError

logger = get_logger(__name__)


@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    version: Optional[str] = None
    required_version: Optional[str] = None
    installed: bool = False
    error_message: Optional[str] = None


class DependencyChecker:
    """依赖检查器"""
    
    # 核心依赖定义
    CORE_DEPENDENCIES = {
        'torch': {
            'import_name': 'torch',
            'min_version': '2.0.0',
            'check_func': '_check_torch'
        },
        'transformers': {
            'import_name': 'transformers',
            'min_version': '4.30.0',
            'check_func': '_check_transformers'
        },
        'sentence_transformers': {
            'import_name': 'sentence_transformers',
            'min_version': '2.2.0',
            'check_func': None
        },
        'chromadb': {
            'import_name': 'chromadb',
            'min_version': '0.4.0',
            'check_func': '_check_chromadb'
        },
        'whoosh': {
            'import_name': 'whoosh',
            'min_version': '2.7.4',
            'check_func': None
        }
    }
    
    # UI依赖
    UI_DEPENDENCIES = {
        'PyQt6': {
            'import_name': 'PyQt6',
            'min_version': '6.4.0',
            'check_func': '_check_pyqt6'
        },
        'PyQt6_WebEngine': {
            'import_name': 'PyQt6.QtWebEngineWidgets',
            'min_version': '6.4.0',
            'check_func': None
        }
    }
    
    # 文档处理依赖
    DOC_DEPENDENCIES = {
        'fitz': {
            'import_name': 'fitz',
            'min_version': '1.23.0',
            'check_func': None
        },
        'docx': {
            'import_name': 'docx',
            'min_version': '0.8.11',
            'check_func': None
        },
        'openpyxl': {
            'import_name': 'openpyxl',
            'min_version': '3.1.0',
            'check_func': None
        }
    }
    
    # 可选依赖
    OPTIONAL_DEPENDENCIES = {
        'psutil': {
            'import_name': 'psutil',
            'min_version': '5.9.0',
            'check_func': None
        },
        'pytesseract': {
            'import_name': 'pytesseract',
            'min_version': '0.3.10',
            'check_func': None
        }
    }
    
    def __init__(self):
        self.results: Dict[str, DependencyInfo] = {}
    
    def check_all_dependencies(self) -> Dict[str, List[DependencyInfo]]:
        """检查所有依赖"""
        logger.info("开始检查依赖...")
        
        results = {
            'core': self._check_dependency_group(self.CORE_DEPENDENCIES),
            'ui': self._check_dependency_group(self.UI_DEPENDENCIES),
            'document': self._check_dependency_group(self.DOC_DEPENDENCIES),
            'optional': self._check_dependency_group(self.OPTIONAL_DEPENDENCIES)
        }
        
        # 生成报告
        self._generate_report(results)
        
        return results
    
    def _check_dependency_group(self, dependencies: Dict) -> List[DependencyInfo]:
        """检查依赖组"""
        results = []
        
        for name, config in dependencies.items():
            try:
                dep_info = self._check_single_dependency(name, config)
                results.append(dep_info)
            except Exception as e:
                logger.error(f"检查依赖 {name} 时出错: {e}")
                results.append(DependencyInfo(
                    name=name,
                    installed=False,
                    error_message=str(e)
                ))
        
        return results
    
    def _check_single_dependency(self, name: str, config: Dict) -> DependencyInfo:
        """检查单个依赖"""
        import_name = config['import_name']
        min_version = config.get('min_version')
        check_func = config.get('check_func')
        
        try:
            # 尝试导入模块
            module = importlib.import_module(import_name)
            
            # 获取版本信息
            version = self._get_module_version(module, name)
            
            # 执行特定检查
            if check_func and hasattr(self, check_func):
                check_method = getattr(self, check_func)
                check_result = check_method(module)
                if not check_result[0]:
                    return DependencyInfo(
                        name=name,
                        version=version,
                        required_version=min_version,
                        installed=False,
                        error_message=check_result[1]
                    )
            
            # 版本检查
            version_ok = True
            if min_version and version:
                version_ok = self._compare_versions(version, min_version)
            
            return DependencyInfo(
                name=name,
                version=version,
                required_version=min_version,
                installed=version_ok,
                error_message=None if version_ok else f"版本过低，需要 >= {min_version}"
            )
            
        except ImportError as e:
            return DependencyInfo(
                name=name,
                required_version=min_version,
                installed=False,
                error_message=f"模块未安装: {e}"
            )
    
    def _get_module_version(self, module, name: str) -> Optional[str]:
        """获取模块版本"""
        version_attrs = ['__version__', 'version', 'VERSION']

        for attr in version_attrs:
            if hasattr(module, attr):
                version = getattr(module, attr)
                if isinstance(version, str):
                    return version
                elif isinstance(version, tuple):
                    # 处理元组版本号（如whoosh）
                    return '.'.join(map(str, version))
                elif hasattr(version, '__str__'):
                    return str(version)

        # 特殊处理
        if name == 'fitz':
            try:
                return module.version[0]
            except:
                pass
        elif name == 'whoosh':
            try:
                # whoosh的版本是元组格式
                if hasattr(module, 'versionstring'):
                    return module.versionstring()
                elif hasattr(module, '__version__'):
                    version = module.__version__
                    if isinstance(version, tuple):
                        return '.'.join(map(str, version))
            except:
                pass

        return None
    
    def _compare_versions(self, current: str, required: str) -> bool:
        """比较版本号"""
        try:
            from packaging import version
            return version.parse(current) >= version.parse(required)
        except ImportError:
            # 简单的版本比较
            current_parts = [int(x) for x in current.split('.') if x.isdigit()]
            required_parts = [int(x) for x in required.split('.') if x.isdigit()]
            
            # 补齐长度
            max_len = max(len(current_parts), len(required_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            required_parts.extend([0] * (max_len - len(required_parts)))
            
            return current_parts >= required_parts
    
    def _check_torch(self, module) -> Tuple[bool, Optional[str]]:
        """检查PyTorch"""
        try:
            # 检查CUDA支持
            cuda_available = module.cuda.is_available()
            logger.info(f"PyTorch CUDA支持: {cuda_available}")
            
            if cuda_available:
                gpu_count = module.cuda.device_count()
                logger.info(f"可用GPU数量: {gpu_count}")
            
            return True, None
        except Exception as e:
            return False, f"PyTorch检查失败: {e}"
    
    def _check_transformers(self, module) -> Tuple[bool, Optional[str]]:
        """检查Transformers"""
        try:
            # 检查关键组件
            from transformers import AutoTokenizer, AutoModel
            return True, None
        except ImportError as e:
            return False, f"Transformers组件缺失: {e}"
    
    def _check_chromadb(self, module) -> Tuple[bool, Optional[str]]:
        """检查ChromaDB"""
        try:
            # 检查基本功能
            client = module.Client()
            return True, None
        except Exception as e:
            return False, f"ChromaDB初始化失败: {e}"
    
    def _check_pyqt6(self, module) -> Tuple[bool, Optional[str]]:
        """检查PyQt6"""
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            return True, None
        except ImportError as e:
            return False, f"PyQt6组件缺失: {e}"
    
    def _generate_report(self, results: Dict[str, List[DependencyInfo]]):
        """生成依赖检查报告"""
        logger.info("=== 依赖检查报告 ===")
        
        for category, deps in results.items():
            logger.info(f"\n{category.upper()} 依赖:")
            
            for dep in deps:
                status = "✓" if dep.installed else "✗"
                version_info = f" (v{dep.version})" if dep.version else ""
                error_info = f" - {dep.error_message}" if dep.error_message else ""
                
                logger.info(f"  {status} {dep.name}{version_info}{error_info}")
    
    def get_missing_dependencies(self) -> List[str]:
        """获取缺失的依赖"""
        results = self.check_all_dependencies()
        missing = []
        
        for category, deps in results.items():
            if category != 'optional':  # 可选依赖不算缺失
                for dep in deps:
                    if not dep.installed:
                        missing.append(dep.name)
        
        return missing
    
    def validate_environment(self) -> bool:
        """验证环境是否满足要求"""
        missing = self.get_missing_dependencies()
        
        if missing:
            logger.error(f"缺少必要依赖: {missing}")
            logger.error("请运行: pip install -r requirements.txt")
            return False
        
        logger.info("所有必要依赖已满足")
        return True


# 全局实例
_dependency_checker = None


def get_dependency_checker() -> DependencyChecker:
    """获取依赖检查器实例"""
    global _dependency_checker
    if _dependency_checker is None:
        _dependency_checker = DependencyChecker()
    return _dependency_checker


def check_dependencies() -> bool:
    """快速依赖检查"""
    checker = get_dependency_checker()
    return checker.validate_environment()
