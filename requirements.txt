# 公司制度本地查询平台


# 核心依赖 - 支持Qwen模型
torch>=2.0.0,<2.3.0
transformers>=4.37.0,<4.45.0
sentence-transformers>=4.0.0,<5.0.0
chromadb>=0.4.0,<0.5.0
whoosh>=2.7.4,<2.8.0
modelscope>=1.9.0,<1.30.0

# 文档处理 - 稳定版本
PyMuPDF>=1.23.0,<1.24.0
python-docx>=0.8.11,<1.2.0
openpyxl>=3.1.0,<3.2.0
pytesseract>=0.3.10,<0.4.0
pdf2image>=1.16.0,<1.17.0
Pillow>=10.0.0,<12.0.0
unstructured[pdf]>=0.10.0,<0.11.0

# UI框架 - 扩展版本范围
PyQt6>=6.4.0,<7.0.0
PyQt6-WebEngine>=6.4.0,<7.0.0

# 中文处理
jieba>=0.42.1,<0.43.0
zhconv>=1.4.3,<1.5.0

# 工具库 - 扩展兼容版本
numpy>=1.24.0,<1.26.0
pandas>=2.0.0,<2.2.0
tqdm>=4.65.0,<4.68.0
requests>=2.31.0,<2.33.0
pydantic>=2.0.0,<3.0.0

# 系统监控
psutil>=5.9.0,<8.0.0

# 日志和配置
loguru>=0.7.0,<0.8.0
pyyaml>=6.0,<7.0
python-dotenv>=1.0.0,<1.2.0

# 打包工具
pyinstaller>=5.13.0,<7.0.0
auto-py-to-exe>=2.40.0,<3.0.0

# GPU加速支持（可选）
accelerate>=1.8.0,<2.0.0
safetensors>=0.5.0,<1.0.0
# torch-audio  # 如果需要音频处理

# 开发工具
pytest>=7.4.0,<8.0.0
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0

# 额外的向量检索支持（可选）
# faiss-cpu>=1.8.0,<2.0.0  # 高性能向量检索


