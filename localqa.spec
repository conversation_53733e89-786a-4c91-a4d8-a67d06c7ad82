# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        ('models/*', 'models'),
        ('config.yaml', '.'),
        ('data/*', 'data'),
        ('static/*', 'static'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtWebEngineWidgets',
        'sentence_transformers',
        'transformers',
        'torch',
        'chromadb',
        'whoosh',
    ],
    hookspath=[],
    runtime_hooks=[],
    excludes=[
        'onnx.reference',
        'onnx.reference.ops',
        'onnx.reference.ops.aionnxml',
        'onnx.reference.ops.experimental',
        'onnx.reference.ops.aionnx_preview_training',
        'onnx.reference.ops_optimized',
        'tensorboard',
        'matplotlib.tests',
        'numpy.tests',
        'scipy.tests',
        'pandas.tests',
        'torch.testing',
        'test',
        'tests',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='LocalQA',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 如为GUI程序可改为False
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='LocalQA'
) 