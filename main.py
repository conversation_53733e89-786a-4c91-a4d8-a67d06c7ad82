#!/usr/bin/env python3
"""
公司制度本地查询平台 - 主程序入口
"""
import sys
import os
import multiprocessing
from pathlib import Path

# 设置性能优化环境变量（必须在导入torch之前）
def setup_performance_environment():
    """设置性能优化环境变量"""
    cpu_count = multiprocessing.cpu_count()
    optimal_threads = min(cpu_count, 8)

    # 设置环境变量（在导入torch之前）
    env_vars = {
        'OMP_NUM_THREADS': str(optimal_threads),
        'MKL_NUM_THREADS': str(optimal_threads),
        'NUMEXPR_NUM_THREADS': str(optimal_threads),
        'OPENBLAS_NUM_THREADS': str(optimal_threads),
        'TORCH_NUM_THREADS': str(optimal_threads),
        'MKL_ENABLE_INSTRUCTIONS': 'AVX2'
    }

    for var, value in env_vars.items():
        os.environ[var] = value

    try:
        print(f"性能环境优化完成，使用{optimal_threads}个线程")
    except UnicodeEncodeError:
        print(f"Performance environment optimized, using {optimal_threads} threads")

# 立即设置环境变量
setup_performance_environment()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config import get_config
from src.utils.logger import setup_logger, get_logger
from src.utils.helpers import get_system_info, check_system_requirements
from src.utils.dependency_checker import check_dependencies
from src.utils.performance_monitor import start_global_monitoring, stop_global_monitoring


def check_environment():
    """检查运行环境"""
    logger = get_logger(__name__)
    
    # 检查系统要求
    requirements = check_system_requirements()
    system_info = get_system_info()
    
    logger.info("=== 系统信息 ===")
    for key, value in system_info.items():
        logger.info(f"{key}: {value}")
    
    logger.info("=== 系统要求检查 ===")
    for key, value in requirements.items():
        status = "✓" if value else "✗"
        logger.info(f"{status} {key}: {value}")
    
    # 检查关键要求
    if not requirements['memory_sufficient']:
        logger.error("内存不足！至少需要8GB内存")
        return False
    
    if not requirements['python_version_ok']:
        logger.error("Python版本过低！至少需要Python 3.8")
        return False
    
    return True


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logger()
        logger = get_logger(__name__)
        
        logger.info("=== 公司制度本地查询平台启动 ===")
        
        # 检查环境
        if not check_environment():
            logger.error("环境检查失败，程序退出")
            sys.exit(1)

        # 检查依赖（在打包环境中跳过）
        if not getattr(sys, 'frozen', False):
            logger.info("检查Python依赖...")
            if not check_dependencies():
                logger.error("依赖检查失败，程序退出")
                sys.exit(1)
        else:
            logger.info("检测到打包环境，跳过依赖检查")
        
        # 加载配置
        config = get_config()
        logger.info(f"配置加载完成: {config.ui.window_title}")

        # 启动性能监控
        logger.info("启动性能监控...")
        start_global_monitoring(interval=10.0)  # 每10秒监控一次
        
        # 检查必要目录
        required_dirs = [
            config.data_dir,
            config.models_dir,
            config.docs_dir
        ]
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                logger.warning(f"目录不存在，将创建: {dir_path}")
                os.makedirs(dir_path, exist_ok=True)
        
        # 启动GUI应用
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            logger.info("PyQt6导入成功")
        except Exception as e:
            logger.error(f"PyQt6导入失败: {e}")
            raise

        try:
            from src.ui.main_window import MainWindow
            logger.info("主窗口模块导入成功")
        except Exception as e:
            logger.error(f"主窗口模块导入失败: {e}")
            raise

        # 设置OpenGL上下文共享，解决QtWebEngineWidgets导入问题
        try:
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts)
            logger.info("OpenGL上下文设置成功")
        except Exception as e:
            logger.warning(f"OpenGL上下文设置失败: {e}")
            # 继续执行，这不是致命错误

        app = QApplication(sys.argv)
        app.setApplicationName(config.ui.window_title)
        app.setApplicationVersion("1.0.0")
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        logger.info("GUI界面启动成功")
        
        # 运行应用
        try:
            sys.exit(app.exec())
        finally:
            # 停止性能监控
            stop_global_monitoring()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖包: pip install -r requirements.txt")
        print("🔧 或运行依赖检查: python -m src.utils.dependency_checker")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        try:
            stop_global_monitoring()
        except:
            pass
        sys.exit(0)
    except Exception as e:
        try:
            logger = get_logger(__name__)
            logger.error(f"程序启动失败: {e}", exc_info=True)
        except:
            print(f"❌ 程序启动失败: {e}")
            print("📋 请检查日志文件: logs/app.log")
            print("🔧 或运行诊断: python -c \"from src.utils.helpers import check_system_requirements; print(check_system_requirements())\"")
        sys.exit(1)


if __name__ == "__main__":
    main()
