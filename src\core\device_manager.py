"""
设备管理模块
管理CPU/GPU设备切换和性能优化
"""
import os
import multiprocessing
from typing import Dict, Any, Optional
from enum import Enum

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    torch = None
    TORCH_AVAILABLE = False
except Exception:
    # 处理打包环境中的torch导入问题
    torch = None
    TORCH_AVAILABLE = False

from ..utils.config import get_config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DeviceType(Enum):
    """设备类型枚举"""
    AUTO = "auto"
    CPU = "cpu"
    GPU = "cuda"


class DeviceManager:
    """设备管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.current_device = None
        self.device_info = {}
        self._detect_devices()
    
    def _detect_devices(self):
        """检测可用设备"""
        self.device_info = {
            'cpu_available': True,
            'cpu_cores': multiprocessing.cpu_count(),
            'gpu_available': False,
            'gpu_memory_gb': 0,
            'gpu_name': None
        }
        
        if TORCH_AVAILABLE and torch is not None:
            try:
                if torch.cuda.is_available():
                    self.device_info['gpu_available'] = True
                    self.device_info['gpu_memory_gb'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    self.device_info['gpu_name'] = torch.cuda.get_device_name(0)
                    logger.info(f"检测到GPU: {self.device_info['gpu_name']}, 显存: {self.device_info['gpu_memory_gb']:.1f}GB")
            except Exception as e:
                logger.warning(f"GPU检测失败: {e}")
        
        logger.info(f"CPU核心数: {self.device_info['cpu_cores']}")
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        return self.device_info.copy()
    
    def get_optimal_device(self) -> str:
        """获取最优设备"""
        if self.device_info['gpu_available'] and self.device_info['gpu_memory_gb'] >= 6:
            return "cuda"
        return "cpu"
    
    def switch_device(self, device_type: str) -> bool:
        """切换设备"""
        try:
            if device_type == "auto":
                target_device = self.get_optimal_device()
            elif device_type == "cpu":
                target_device = "cpu"
            elif device_type in ["cuda", "gpu"]:
                if not self.device_info['gpu_available']:
                    logger.error("GPU不可用，无法切换到GPU模式")
                    return False
                target_device = "cuda"
            else:
                logger.error(f"不支持的设备类型: {device_type}")
                return False
            
            # 应用设备优化
            if target_device == "cpu":
                self._apply_cpu_optimization()
            else:
                self._apply_gpu_optimization()
            
            self.current_device = target_device
            logger.info(f"设备已切换到: {target_device}")
            return True
            
        except Exception as e:
            logger.error(f"设备切换失败: {e}")
            return False
    
    def _apply_cpu_optimization(self):
        """应用CPU优化"""
        logger.info("应用CPU性能优化...")

        # 设置环境变量
        cpu_count = self.device_info['cpu_cores']
        optimal_threads = min(cpu_count, 8)

        # 设置环境变量（这些总是可以安全设置的）
        env_vars = {
            'OMP_NUM_THREADS': str(optimal_threads),
            'MKL_NUM_THREADS': str(optimal_threads),
            'NUMEXPR_NUM_THREADS': str(optimal_threads),
            'OPENBLAS_NUM_THREADS': str(optimal_threads),
            'MKL_ENABLE_INSTRUCTIONS': 'AVX2',
            'TORCH_NUM_THREADS': str(optimal_threads)
        }

        for var, value in env_vars.items():
            os.environ[var] = value

        if torch is not None:
            try:
                # 检查是否已经有并行工作在运行
                # 如果PyTorch已经初始化了线程池，就不再尝试设置
                if not hasattr(torch, '_C') or not hasattr(torch._C, '_get_num_threads'):
                    # PyTorch还未完全初始化，可以安全设置
                    torch.set_num_threads(optimal_threads)
                    torch.set_num_interop_threads(optimal_threads)
                    logger.info(f"PyTorch线程数设置为: {optimal_threads}")
                else:
                    # PyTorch已经初始化，检查当前设置
                    current_threads = torch.get_num_threads()
                    if current_threads != optimal_threads:
                        logger.info(f"PyTorch已初始化，当前线程数: {current_threads}，期望: {optimal_threads}")
                        logger.info("将通过环境变量TORCH_NUM_THREADS影响新的进程")
                    else:
                        logger.info(f"PyTorch线程数已正确设置: {current_threads}")

                # 启用MKL优化（这个总是可以安全设置的）
                if hasattr(torch.backends, 'mkl') and torch.backends.mkl.is_available():
                    torch.backends.mkl.enabled = True
                    logger.info("Intel MKL优化已启用")

                # 设置CPU特定的优化
                if hasattr(torch.backends, 'mkldnn'):
                    torch.backends.mkldnn.enabled = True
                    logger.info("Intel MKL-DNN优化已启用")

            except Exception as e:
                logger.warning(f"PyTorch优化设置失败: {e}")
                logger.info("将依赖环境变量进行优化")

        logger.info(f"CPU优化完成: 使用{optimal_threads}个线程")
    
    def _apply_gpu_optimization(self):
        """应用GPU优化"""
        logger.info("应用GPU性能优化...")
        
        if torch is not None and torch.cuda.is_available():
            # 启用CUDA优化
            torch.backends.cudnn.enabled = True
            torch.backends.cudnn.benchmark = True
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
        
        logger.info("GPU优化完成")
    
    def get_performance_config(self, device: str) -> Dict[str, Any]:
        """获取设备对应的性能配置"""
        if device == "cpu":
            # CPU模式超激进优化配置 - 确保15秒内完成
            return {
                'max_tokens': 400,   # 激进降低到400
                'batch_size': 1,     # 单批处理
                'use_cache': True,   # 启用缓存
                'num_beams': 1,      # 贪婪搜索
                'early_stopping': False,  # num_beams=1时不使用
                'do_sample': False,  # 确定性生成
                'max_new_tokens': 80,   # 激进降低到80个token
                'repetition_penalty': 1.01,  # 最小重复惩罚
                'pad_token_id': None,   # 将在运行时设置
                'eos_token_id': None,   # 将在运行时设置
                # 移除max_length避免冲突
            }
        else:  # GPU
            return {
                'max_tokens': 2048,
                'temperature': 0.7,
                'batch_size': 4,
                'use_cache': True,
                'num_beams': 4,
                'early_stopping': False,
                'do_sample': True,
                'top_p': 0.8,
                'top_k': 50,
                'repetition_penalty': 1.1,
                'max_new_tokens': 1024,
            }
    
    def get_device_status(self) -> Dict[str, Any]:
        """获取设备状态"""
        status = {
            'current_device': self.current_device,
            'device_info': self.device_info,
            'performance_mode': 'CPU优化' if self.current_device == 'cpu' else 'GPU加速'
        }
        
        if torch is not None:
            status['torch_device'] = str(torch.cuda.current_device()) if torch.cuda.is_available() else 'cpu'
            status['torch_threads'] = torch.get_num_threads()
        
        return status


# 全局设备管理器实例
_device_manager = None


def get_device_manager() -> DeviceManager:
    """获取设备管理器实例（单例模式）"""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager()
    return _device_manager


def switch_device(device_type: str) -> bool:
    """切换设备的便捷函数"""
    return get_device_manager().switch_device(device_type)


def get_current_device() -> Optional[str]:
    """获取当前设备"""
    return get_device_manager().current_device


def get_device_info() -> Dict[str, Any]:
    """获取设备信息的便捷函数"""
    return get_device_manager().get_device_info()
