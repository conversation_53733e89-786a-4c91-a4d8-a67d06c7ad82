"""
全文索引模块
使用Whoosh引擎构建全文搜索索引，支持中文分词
"""
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import re

try:
    from whoosh import fields, index
    from whoosh.qparser import QueryParser, MultifieldParser
    from whoosh.query import And, Or, Term
    from whoosh.analysis import StandardAnalyzer, Tokenizer, Token
    from whoosh.filedb.filestore import FileStorage
except ImportError:
    fields = index = QueryParser = MultifieldParser = None
    And = Or = Term = StandardAnalyzer = FileStorage = None
    Tokenizer = Token = None

try:
    import jieba
    import jieba.analyse
except ImportError:
    jieba = None

from .document_processor import DocumentChunk
from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import ProgressTracker, ensure_dir

logger = get_logger(__name__)

class ChineseTokenizer(Tokenizer):
    """中文分词器"""

    def __init__(self):
        if jieba is None:
            raise ImportError("jieba未安装，请运行: pip install jieba")
        if Tokenizer is None:
            raise ImportError("Whoosh未安装，请运行: pip install whoosh")

        # 设置jieba模式
        jieba.setLogLevel(20)  # 减少日志输出

    def __call__(self, value, positions=False, chars=False, keeporiginal=False, removestops=True, start_pos=0, start_char=0, tokenize=True, mode='', **kwargs):
        """分词处理"""
        assert isinstance(value, str), f"Expected str, got {type(value)}"

        # 使用jieba进行中文分词
        tokens = jieba.cut_for_search(value)

        pos = start_pos
        char_pos = start_char

        for token_text in tokens:
            token_text = token_text.strip().lower()
            if len(token_text) > 1 and not re.match(r'^[0-9\s\W]+$', token_text):
                token = Token(token_text)
                token.text = token_text  # 确保设置text属性
                if positions:
                    token.pos = pos
                if chars:
                    token.startchar = char_pos
                    token.endchar = char_pos + len(token_text)

                yield token
                pos += 1
                char_pos += len(token_text)


class TextIndex:
    """全文索引管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.index_dir = Path(self.config.database.whoosh_index_dir)
        self.index = None
        self.schema = None
        
        # 初始化
        self._setup_schema()
        self._initialize_index()
    
    def _setup_schema(self):
        """设置索引模式"""
        if fields is None:
            raise ImportError("Whoosh未安装，请运行: pip install whoosh")
        
        # 创建中文分析器
        try:
            from whoosh.analysis import RegexTokenizer, LowercaseFilter
            chinese_tokenizer = ChineseTokenizer()
            chinese_analyzer = chinese_tokenizer | LowercaseFilter()
        except Exception as e:
            logger.warning(f"中文分析器创建失败，使用标准分析器: {e}")
            chinese_analyzer = StandardAnalyzer()

        # 定义索引字段
        self.schema = fields.Schema(
            id=fields.ID(stored=True, unique=True),
            content=fields.TEXT(analyzer=chinese_analyzer, stored=True),
            title=fields.TEXT(analyzer=chinese_analyzer, stored=True),
            department=fields.TEXT(analyzer=chinese_analyzer, stored=True),
            file_name=fields.TEXT(analyzer=chinese_analyzer, stored=True),
            file_path=fields.TEXT(stored=True),
            page_number=fields.NUMERIC(stored=True),
            chunk_index=fields.NUMERIC(stored=True),
            document_type=fields.TEXT(stored=True),
            file_hash=fields.TEXT(stored=True),
            creation_date=fields.TEXT(stored=True)
        )
    
    def _initialize_index(self):
        """初始化索引"""
        try:
            # 确保索引目录存在
            ensure_dir(self.index_dir)
            
            # 检查索引是否存在
            if index.exists_in(str(self.index_dir)):
                self.index = index.open_dir(str(self.index_dir))
                logger.info(f"加载现有索引: {self.index_dir}")
            else:
                self.index = index.create_in(str(self.index_dir), self.schema)
                logger.info(f"创建新索引: {self.index_dir}")
                
        except Exception as e:
            logger.error(f"索引初始化失败: {e}")
            raise
    
    def add_chunks(self, chunks: List[DocumentChunk]) -> bool:
        """添加文档分块到索引"""
        if not chunks:
            logger.warning("没有分块需要添加到索引")
            return True
        
        try:
            logger.info(f"开始添加 {len(chunks)} 个分块到全文索引")
            
            writer = self.index.writer()
            
            for chunk in chunks:
                # 提取标题（从内容的前100个字符）
                title = chunk.content[:100].replace('\n', ' ').strip()
                
                # 添加文档
                writer.add_document(
                    id=chunk.id,
                    content=chunk.content,
                    title=title,
                    department=chunk.metadata.get('department', ''),
                    file_name=chunk.metadata.get('file_name', ''),
                    file_path=chunk.metadata.get('file_path', ''),
                    page_number=chunk.metadata.get('page_number', 0),
                    chunk_index=chunk.metadata.get('chunk_index', 0),
                    document_type=chunk.metadata.get('document_type', ''),
                    file_hash=chunk.metadata.get('file_hash', ''),
                    creation_date=chunk.metadata.get('creation_date', '')
                )
            
            writer.commit()
            logger.info(f"成功添加 {len(chunks)} 个分块到全文索引")
            return True
            
        except Exception as e:
            logger.error(f"添加分块到索引失败: {e}")
            return False
    
    def search(self, query: str, top_k: int = 10, 
              department: Optional[str] = None,
              document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """全文搜索"""
        try:
            with self.index.searcher() as searcher:
                # 构建查询解析器
                parser = MultifieldParser(
                    ["content", "title", "file_name"], 
                    self.index.schema
                )
                
                # 解析查询
                parsed_query = parser.parse(query)
                
                # 添加过滤条件
                filters = []
                if department:
                    filters.append(Term("department", department))
                if document_type:
                    filters.append(Term("document_type", document_type))
                
                if filters:
                    if len(filters) == 1:
                        final_query = And([parsed_query, filters[0]])
                    else:
                        final_query = And([parsed_query] + filters)
                else:
                    final_query = parsed_query
                
                # 执行搜索
                results = searcher.search(final_query, limit=top_k)
                
                # 格式化结果
                search_results = []
                for hit in results:
                    result = {
                        'id': hit['id'],
                        'content': hit['content'],
                        'title': hit['title'],
                        'file_name': hit['file_name'],
                        'file_path': hit['file_path'],
                        'department': hit['department'],
                        'page_number': hit['page_number'],
                        'chunk_index': hit['chunk_index'],
                        'document_type': hit['document_type'],
                        'score': hit.score,
                        'rank': hit.rank
                    }
                    search_results.append(result)
                
                logger.info(f"全文搜索完成，查询: '{query}', 返回 {len(search_results)} 个结果")
                return search_results
                
        except Exception as e:
            logger.error(f"全文搜索失败: {e}")
            return []
    
    def search_by_keywords(self, keywords: List[str], top_k: int = 10) -> List[Dict[str, Any]]:
        """关键词搜索"""
        try:
            # 构建查询字符串
            query_str = " OR ".join(keywords)
            return self.search(query_str, top_k)
            
        except Exception as e:
            logger.error(f"关键词搜索失败: {e}")
            return []
    
    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            with self.index.searcher() as searcher:
                doc_count = searcher.doc_count()
                
            return {
                'total_documents': doc_count,
                'index_dir': str(self.index_dir),
                'schema_fields': list(self.schema.names())
            }
            
        except Exception as e:
            logger.error(f"获取索引统计失败: {e}")
            return {}
    
    def delete_by_source(self, source_file: str) -> bool:
        """根据源文件删除索引"""
        try:
            writer = self.index.writer()
            writer.delete_by_term('file_path', source_file)
            writer.commit()
            
            logger.info(f"删除文件 {source_file} 的索引")
            return True
            
        except Exception as e:
            logger.error(f"删除索引失败: {e}")
            return False
    
    def clear_index(self) -> bool:
        """清空索引"""
        try:
            # 方法1：尝试删除所有文档
            try:
                writer = self.index.writer()

                # 使用reader获取所有文档
                with self.index.searcher() as searcher:
                    # 获取所有文档并删除
                    for docnum in range(searcher.doc_count_all()):
                        try:
                            writer.delete_document(docnum)
                        except:
                            continue

                writer.commit()
                logger.info("全文索引已清空（方法1）")
                return True

            except Exception as e1:
                logger.warning(f"方法1清空失败: {e1}")

                # 方法2：重新创建索引
                try:
                    # 关闭当前索引
                    if hasattr(self.index, 'close'):
                        self.index.close()

                    # 删除索引目录并重新创建
                    import shutil
                    if self.index_dir.exists():
                        shutil.rmtree(self.index_dir)

                    # 重新初始化索引
                    self._initialize_index()

                    logger.info("全文索引已清空（方法2）")
                    return True

                except Exception as e2:
                    logger.error(f"方法2清空失败: {e2}")
                    return False

        except Exception as e:
            logger.error(f"清空索引失败: {e}")
            return False
    
    def batch_add_chunks(self, chunks: List[DocumentChunk], batch_size: int = 1000) -> bool:
        """批量添加分块（大数据量优化）"""
        if not chunks:
            return True
        
        try:
            total_batches = (len(chunks) + batch_size - 1) // batch_size
            progress = ProgressTracker(total_batches, "索引构建")
            
            for i in range(0, len(chunks), batch_size):
                batch_chunks = chunks[i:i + batch_size]
                
                if not self.add_chunks(batch_chunks):
                    logger.error(f"批次 {i//batch_size + 1} 索引失败")
                    return False
                
                progress.update()
            
            progress.finish()
            logger.info(f"批量索引完成，总共处理 {len(chunks)} 个分块")
            return True
            
        except Exception as e:
            logger.error(f"批量索引失败: {e}")
            return False
    
    def suggest_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词建议"""
        try:
            if jieba is None:
                return []
            
            # 使用jieba提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
            return keywords
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    def get_departments(self) -> List[str]:
        """获取所有部门列表"""
        try:
            with self.index.searcher() as searcher:
                departments = set()
                
                # 遍历所有文档获取部门信息
                for doc in searcher.all_doc_ids():
                    stored_fields = searcher.stored_fields(doc)
                    dept = stored_fields.get('department', '').strip()
                    if dept:
                        departments.add(dept)
                
                return sorted(list(departments))
                
        except Exception as e:
            logger.error(f"获取部门列表失败: {e}")
            return []
    
    def get_document_types(self) -> List[str]:
        """获取所有文档类型列表"""
        try:
            with self.index.searcher() as searcher:
                doc_types = set()
                
                # 遍历所有文档获取类型信息
                for doc in searcher.all_doc_ids():
                    stored_fields = searcher.stored_fields(doc)
                    doc_type = stored_fields.get('document_type', '').strip()
                    if doc_type:
                        doc_types.add(doc_type)
                
                return sorted(list(doc_types))
                
        except Exception as e:
            logger.error(f"获取文档类型列表失败: {e}")
            return []
