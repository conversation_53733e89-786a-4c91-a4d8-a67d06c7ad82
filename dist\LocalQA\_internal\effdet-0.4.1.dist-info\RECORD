effdet-0.4.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
effdet-0.4.1.dist-info/LICENSE,sha256=uiRMMX3DBSEXPihEgJKFngvGXl-PIwZ36jpRuOCHbY8,11343
effdet-0.4.1.dist-info/METADATA,sha256=XV0vzJIM9DnOe7yxkmoYlRehk7KSWWVk_m2Df_39T74,33304
effdet-0.4.1.dist-info/RECORD,,
effdet-0.4.1.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
effdet-0.4.1.dist-info/top_level.txt,sha256=_RHemJgDcv8soMqmgUXnpJ1BraILPBxZfgr0GQJCBDQ,7
effdet/__init__.py,sha256=MRgPSsdow1uPP7WipOLI2AcUCi-ln2kj75kVVRdRyTQ,479
effdet/__pycache__/__init__.cpython-310.pyc,,
effdet/__pycache__/anchors.cpython-310.pyc,,
effdet/__pycache__/bench.cpython-310.pyc,,
effdet/__pycache__/distributed.cpython-310.pyc,,
effdet/__pycache__/efficientdet.cpython-310.pyc,,
effdet/__pycache__/evaluator.cpython-310.pyc,,
effdet/__pycache__/factory.cpython-310.pyc,,
effdet/__pycache__/helpers.cpython-310.pyc,,
effdet/__pycache__/loss.cpython-310.pyc,,
effdet/__pycache__/soft_nms.cpython-310.pyc,,
effdet/__pycache__/version.cpython-310.pyc,,
effdet/anchors.py,sha256=PrEpSAuVfXjhunR-H3rdNmEYjpJx3U4zGObcGpLFZ48,17224
effdet/bench.py,sha256=BcMMl4Oe4bAxg4SEOyOAxFjMXtf9lEjMJvVgTr8-8U4,7962
effdet/config/__init__.py,sha256=T-L2ERWbJoRO50zaOxS2lS9B5UIEjcNd6mFiKyiRb7I,247
effdet/config/__pycache__/__init__.cpython-310.pyc,,
effdet/config/__pycache__/config_utils.cpython-310.pyc,,
effdet/config/__pycache__/fpn_config.cpython-310.pyc,,
effdet/config/__pycache__/model_config.cpython-310.pyc,,
effdet/config/__pycache__/train_config.cpython-310.pyc,,
effdet/config/config_utils.py,sha256=diVX8sGFb9pjJc2Wtsu3NbLmgMGErfFH0W1QxVgaBv8,178
effdet/config/fpn_config.py,sha256=egm7U7OyNm-oVwWh6xR8B-RspufQ77tWKzvMNJM0ZKQ,6758
effdet/config/model_config.py,sha256=PvTZbr0jzy5Zo_qBmRClCm9H8YlYfA0M2UwmdVakHz4,26363
effdet/config/train_config.py,sha256=cB3Vn_AYx7u8ltZ1VGSxop8EC-bbUfPKnRkKgo-BdXw,798
effdet/data/__init__.py,sha256=J2o_kxhtCLdzFtM6JgzC1Aw6TTlnVK9XII4BI4hEXUQ,235
effdet/data/__pycache__/__init__.cpython-310.pyc,,
effdet/data/__pycache__/dataset.cpython-310.pyc,,
effdet/data/__pycache__/dataset_config.cpython-310.pyc,,
effdet/data/__pycache__/dataset_factory.cpython-310.pyc,,
effdet/data/__pycache__/input_config.cpython-310.pyc,,
effdet/data/__pycache__/loader.cpython-310.pyc,,
effdet/data/__pycache__/random_erasing.cpython-310.pyc,,
effdet/data/__pycache__/transforms.cpython-310.pyc,,
effdet/data/dataset.py,sha256=2X6jLxrIDWo9bjujx8cU9ChFBOgtBbzmY8jJ4SH3Ttw,2624
effdet/data/dataset_config.py,sha256=homrUNI5-u-ayNDXxEnhRnI_wyBpkJoaXF5YBBqUFOw,6378
effdet/data/dataset_factory.py,sha256=3_cCWwqrzrBhgJI4PpGrRdtV0OiBsW0e4vx_t_muv8Y,4170
effdet/data/input_config.py,sha256=b4K-pqEuyzlHQHB7AwRovvgTEskzSC02dPfUJ5geI1g,2808
effdet/data/loader.py,sha256=EfAR2rJoLz-0Ry_aZDH4dnNxnEqnfz0Lmq0p04ZLcj0,9187
effdet/data/parsers/__init__.py,sha256=Q62B2ZPcEXu3Ed-pQE-kDZT4fbH_uCcvmyu0XDaSMbg,118
effdet/data/parsers/__pycache__/__init__.cpython-310.pyc,,
effdet/data/parsers/__pycache__/parser.cpython-310.pyc,,
effdet/data/parsers/__pycache__/parser_coco.cpython-310.pyc,,
effdet/data/parsers/__pycache__/parser_config.cpython-310.pyc,,
effdet/data/parsers/__pycache__/parser_factory.cpython-310.pyc,,
effdet/data/parsers/__pycache__/parser_open_images.cpython-310.pyc,,
effdet/data/parsers/__pycache__/parser_voc.cpython-310.pyc,,
effdet/data/parsers/parser.py,sha256=fJVVkhnJbyUTHy8Y2IglR4K9rCrh3dHztrQlQAnsQ8c,3481
effdet/data/parsers/parser_coco.py,sha256=kUULQQYspU5dpd_TpVhdneftf3v1r0IdqSK393X2KTk,3321
effdet/data/parsers/parser_config.py,sha256=-lm9zbzm5Wu8xctE3GUSGDNeUOQYklAVKe0Zs3Npi-M,1182
effdet/data/parsers/parser_factory.py,sha256=AEp6zDuy0SA8vgZv7PfIFxc1FwDWeUO8rs1wV1KGQfA,491
effdet/data/parsers/parser_open_images.py,sha256=Gu8iWYDRf4daUIUQTVRp30JW5dx24kLKIU1ixHgmzMc,9161
effdet/data/parsers/parser_voc.py,sha256=SUv4E_OnvkMDIIwBkGD4Z-FzqalNrGy9wBKWfB5vlxY,5140
effdet/data/random_erasing.py,sha256=wK8BC_SgfZQvONVXQlzIrvb_k1l1g4Qa_UGblalM53o,4422
effdet/data/transforms.py,sha256=7n5NCpY87mpIjw6Z1vC8QM8rVMCSYLVz_JhICWobNRs,9504
effdet/distributed.py,sha256=0nYH1dmOTQrWtaG_BVXDpnC3Oro6DPstQ9ONIpqB8KE,9700
effdet/efficientdet.py,sha256=G_nsw3m6RWfz8PDlzYqdlMF8fo9Npw7XWWvfiYapnRY,28412
effdet/evaluation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
effdet/evaluation/__pycache__/__init__.cpython-310.pyc,,
effdet/evaluation/__pycache__/detection_evaluator.cpython-310.pyc,,
effdet/evaluation/__pycache__/fields.cpython-310.pyc,,
effdet/evaluation/__pycache__/metrics.cpython-310.pyc,,
effdet/evaluation/__pycache__/np_box_list.cpython-310.pyc,,
effdet/evaluation/__pycache__/np_mask_list.cpython-310.pyc,,
effdet/evaluation/__pycache__/object_detection_evaluation.cpython-310.pyc,,
effdet/evaluation/__pycache__/per_image_evaluation.cpython-310.pyc,,
effdet/evaluation/detection_evaluator.py,sha256=NsvHlSF88JW_N7c1KaKQApKPnKYv45QikqF8pHQNGO8,29907
effdet/evaluation/fields.py,sha256=2EU9c356CqJSu6rxyd2gXPoEGYUjZ-it9RiFcgOWv40,4327
effdet/evaluation/metrics.py,sha256=L8MQxFZnmFWgIAhD0iqCBj0g1X8aaP7Kf4eXnrh8n5o,6595
effdet/evaluation/np_box_list.py,sha256=AcP1yIp64B0yDo8MPk99HRHPQNP7jxF_DxThtVDrTGI,27721
effdet/evaluation/np_mask_list.py,sha256=m5VW11iK7s-ldhUIl4NJxxhVFXf0vnWWKDVnGwf_AEw,19429
effdet/evaluation/object_detection_evaluation.py,sha256=EIq4smQirr89ngp57vnrT-vb2602ZpqDCB-oSPnlES0,14552
effdet/evaluation/per_image_evaluation.py,sha256=LxehNYsxV0BJjSTdmje8UECgNFD0dbOGE94FULocEcA,31672
effdet/evaluator.py,sha256=ob8tlVnxi9-fIQiVPWcuYCzQ3hMzgvQQTaLIvF1zdAE,7828
effdet/factory.py,sha256=qe-j5I6cbGTNP4F2P58_hFIe9VBD2MzBIh-aKSXkjo0,2186
effdet/helpers.py,sha256=wTEadCJLFERKOuxeg7VHrxNm0BGrtnsE8E95Ylkqg68,737
effdet/loss.py,sha256=F8JS-EKD3DWVmCsBZzvKHscZgyE3p7ky73btdZpSp4s,10720
effdet/object_detection/__init__.py,sha256=Vt3Dsna0AOu0kgJpo2zXcDh0iz9g1E8MWrFNkOh3c4M,1074
effdet/object_detection/__pycache__/__init__.cpython-310.pyc,,
effdet/object_detection/__pycache__/argmax_matcher.cpython-310.pyc,,
effdet/object_detection/__pycache__/box_coder.cpython-310.pyc,,
effdet/object_detection/__pycache__/box_list.cpython-310.pyc,,
effdet/object_detection/__pycache__/matcher.cpython-310.pyc,,
effdet/object_detection/__pycache__/region_similarity_calculator.cpython-310.pyc,,
effdet/object_detection/__pycache__/target_assigner.cpython-310.pyc,,
effdet/object_detection/argmax_matcher.py,sha256=HID5_mmGp5WBB5u7vmqx4S216HV8PGbzeXLoj19HNqE,8550
effdet/object_detection/box_coder.py,sha256=LAAyCTG-JKGONw-SeFLEDFX1w0W3yjK-MfSntaemlfU,6391
effdet/object_detection/box_list.py,sha256=ipZwHM_VTY8YR5zTyu_uVq-ozKcqs237oHEll3L5QwE,7034
effdet/object_detection/matcher.py,sha256=sGEg_i4ksrtAXQsr7fRLnTlhSHX_jKrhMGH2y3TddWg,7492
effdet/object_detection/region_similarity_calculator.py,sha256=rIUaBCgiCdNR6o34T7P1FiJYvUqKG1RbYNt_oQWQAZQ,3610
effdet/object_detection/target_assigner.py,sha256=LCQNr1cjhc2P7kFVQtswXl4U1T0ZL_PrGaYvD2y7jG4,12981
effdet/soft_nms.py,sha256=_MaATcdr5otiMGlOXJXqJKviRUfQTQ3hcpymOfOZ2xk,6229
effdet/version.py,sha256=qBs4HqYsPn6yUHWEuCN4rGUC05gABbl800d8LBd8h9w,22
