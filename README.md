# 公司制度本地查询平台

## 项目概述
基于AI的公司制度文档查询平台，支持智能问答和关键词检索，提供本地化部署方案。

## 功能特性
- **AI智能问答**：基于ChatGLM3-6B模型，提供准确的制度问答服务
- **制度检索**：支持关键词搜索，结果按相关性排序
- **文档预览**：支持PDF/Office文档在线预览和定位
- **来源引用**：所有答案都附带准确的来源引用
- **本地部署**：单文件夹交付，双击即可运行

## 技术架构
- **前端界面**：PyQt6 + QWebEngine
- **AI模型**：ChatGLM3-6B-INT4 (4bit量化)
- **向量数据库**：ChromaDB
- **全文索引**：Whoosh
- **文档处理**：Unstructured + Tesseract OCR
- **文本嵌入**：text2vec-chinese

## 目录结构
```
PolicySearch/
├── main.py                    # 主程序入口
├── src/                       # 源代码目录
│   ├── __init__.py
│   ├── core/                  # 核心功能模块
│   │   ├── __init__.py
│   │   ├── document_processor.py    # 文档处理
│   │   ├── vector_store.py          # 向量数据库
│   │   ├── text_index.py            # 全文索引
│   │   ├── ai_model.py              # AI模型管理
│   │   └── search_system.py         # 搜索系统
│   ├── ui/                    # 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py           # 主窗口
│   │   ├── chat_widget.py           # 问答界面
│   │   ├── search_widget.py         # 搜索界面
│   │   └── preview_widget.py        # 预览界面
│   └── utils/                 # 工具函数
│       ├── __init__.py
│       ├── config.py                # 配置管理
│       ├── logger.py                # 日志管理
│       └── helpers.py               # 辅助函数
├── data/                      # 数据目录
│   ├── chroma_db/            # 向量数据库
│   ├── whoosh_index/         # 全文索引
│   ├── docs/                 # 原始文档
│   └── preview_pdfs/         # 转换后的PDF
├── models/                    # 模型目录
│   ├── text2vec-chinese/     # 文本嵌入模型
│   └── chatglm3-6b-int4/     # 对话模型
├── requirements.txt           # 依赖列表
├── build.py                   # 打包脚本
└── README.md                  # 项目说明
```

## 开发阶段
1. 文档预处理和向量化
2. 索引构建和模型部署
3. 界面开发和功能集成
4. 性能优化和打包部署

## 部署要求
- Windows 10/11
- 内存：8GB+ (推荐16GB)
- 存储：10GB+ 可用空间
- GPU：可选，支持CUDA加速
