"""
应用常量定义
"""
from enum import Enum
from typing import Set, Dict, Any

# 版本信息
APP_VERSION = "1.0.0"
APP_NAME = "公司制度查询平台"

# 支持的文件格式
SUPPORTED_DOCUMENT_FORMATS: Set[str] = {
    '.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt'
}

# 文档类型映射
DOCUMENT_TYPE_MAPPING: Dict[str, str] = {
    '.pdf': 'PDF文档',
    '.docx': 'Word文档',
    '.doc': 'Word文档',
    '.xlsx': 'Excel表格',
    '.xls': 'Excel表格',
    '.txt': '文本文件'
}

# 默认配置值
DEFAULT_CHUNK_SIZE = 512
DEFAULT_CHUNK_OVERLAP = 50
DEFAULT_MAX_TOKENS = 2048
DEFAULT_TEMPERATURE = 0.7
DEFAULT_CACHE_SIZE = 1000

# 系统限制
MAX_FILE_SIZE_MB = 100
MAX_CHUNK_SIZE = 2048
MIN_CHUNK_SIZE = 50
MAX_MEMORY_USAGE_PERCENT = 90

# OCR语言配置
OCR_LANGUAGES = {
    'chinese': 'chi_sim',
    'english': 'eng',
    'chinese_english': 'chi_sim+eng'
}

# 日志级别
class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

# 设备类型
class DeviceType(Enum):
    """设备类型枚举"""
    AUTO = "auto"
    CPU = "cpu"
    CUDA = "cuda"

# 搜索类型
class SearchType(Enum):
    """搜索类型枚举"""
    VECTOR = "vector"
    TEXT = "text"
    HYBRID = "hybrid"

# 任务状态
class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

# 错误代码
class ErrorCode(Enum):
    """错误代码枚举"""
    SUCCESS = 0
    CONFIG_ERROR = 1001
    MODEL_LOAD_ERROR = 1002
    DOCUMENT_PROCESS_ERROR = 1003
    SEARCH_ERROR = 1004
    SYSTEM_ERROR = 1005

# UI主题
class UITheme(Enum):
    """UI主题枚举"""
    LIGHT = "light"
    DARK = "dark"
    CNKI = "cnki"

# CNKI主题颜色
CNKI_COLORS = {
    'primary': '#1a5099',
    'secondary': '#e6f0ff',
    'accent': '#0066cc',
    'background': '#ffffff',
    'surface': '#f8f9fa',
    'text_primary': '#333333',
    'text_secondary': '#666666',
    'border': '#e0e6ed',
    'hover': 'rgba(26, 80, 153, 0.1)'
}

# 性能配置
PERFORMANCE_CONFIG = {
    'max_parallel_workers': 4,
    'batch_size_small': 50,
    'batch_size_medium': 100,
    'batch_size_large': 200,
    'memory_threshold_warning': 80,
    'memory_threshold_critical': 90,
    'retry_max_attempts': 3,
    'retry_delay_seconds': 1.0
}

# 正则表达式模式
REGEX_PATTERNS = {
    'chinese_text': r'[\u4e00-\u9fff]+',
    'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'phone': r'^1[3-9]\d{9}$',
    'safe_filename': r'[<>:"/\\|?*]'
}

# 文件路径模板
PATH_TEMPLATES = {
    'models': 'models/{model_name}',
    'data': 'data/{data_type}',
    'logs': 'logs/{date}.log',
    'cache': 'cache/{cache_type}',
    'temp': 'temp/{temp_id}'
}

# API配置
API_CONFIG = {
    'timeout_seconds': 30,
    'max_retries': 3,
    'rate_limit_per_minute': 60
}

# 数据库配置
DATABASE_CONFIG = {
    'connection_pool_size': 10,
    'query_timeout_seconds': 30,
    'max_connections': 50
}
