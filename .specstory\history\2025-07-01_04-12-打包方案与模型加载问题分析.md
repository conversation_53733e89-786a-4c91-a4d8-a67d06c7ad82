<!-- Generated by SpecStory -->

# 打包方案与模型加载问题分析 (2025-07-01 12:12:03)

_**User**_

认真分析项目，制定一个详细的打包方案，确保客户开箱即用，且所有功能（包括模型加载和推理）都能正常使用。我之前用 PyInstaller 打包后界面正常但模型无法使用。

---

_**Assistant**_

好的，我会为你详细分析项目并制定一个“开箱即用”的打包方案，确保所有功能（包括模型加载和推理）都能正常使用。你遇到的 PyInstaller 打包后模型无法使用的问题，通常是因为模型文件未被正确打包或路径处理不当。下面是详细的分析和方案：

---

## 一、项目结构分析

你的项目结构如下：

```
/d:/LocalQA
├── main.py
├── preprocess.py
├── config.yaml
├── requirements.txt
├── README.md
├── models/           # 可能存放模型文件
├── data/             # 可能存放数据文件
├── static/           # 静态资源
├── src/              # 代码目录
├── logs/             # 日志
├── downloads/        # 下载内容
├── docs/             # 文档
└── .specstory/       # 其他
```

- 主要入口：`main.py`
- 配置文件：`config.yaml`
- 依赖：`requirements.txt`
- 可能的模型目录：`models/`
- 其他资源目录：`data/`, `static/` 等

---

## 二、PyInstaller 打包常见问题

1. **模型文件未被打包**：PyInstaller 默认只打包 Python 代码，外部文件（如模型、配置、数据等）需手动指定。
2. **路径问题**：打包后运行时，`__file__`、相对路径等会失效，需用 PyInstaller 的 `sys._MEIPASS` 机制处理。
3. **依赖缺失**：部分依赖包需在 spec 文件中额外声明。

---

## 三、详细打包方案

### 1. 明确需要包含的文件/文件夹

- `models/`（模型文件）
- `config.yaml`（配置文件）
- `data/`（如有必要）
- `static/`（如有前端界面或静态资源）

### 2. 修改代码以兼容 PyInstaller 路径

在加载模型、配置、数据等文件时，需用如下方式获取正确路径：

```python
import sys
import os

def resource_path(relative_path):
    """获取资源文件的绝对路径，兼容 PyInstaller 打包后和源码运行"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller 打包后
        base_path = sys._MEIPASS
    else:
        # 源码运行
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)
```

使用示例：

```python
model_path = resource_path('models/your_model.bin')
config_path = resource_path('config.yaml')
```

### 3. 编写 PyInstaller spec 文件

使用 spec 文件可以灵活指定需要打包的资源。示例：

```python
# localqa.spec
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        ('models/*', 'models'),      # 打包 models 目录
        ('config.yaml', '.'),        # 打包配置文件
        ('data/*', 'data'),          # 如有需要
        ('static/*', 'static'),      # 如有需要
    ],
    hiddenimports=[],
    hookspath=[],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='LocalQA',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 如果是 GUI 程序改为 False
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='LocalQA'
)
```

### 4. 打包命令

```bash
pyinstaller localqa.spec
```

### 5. 测试

- 在 dist/LocalQA 目录下，双击运行 exe 文件，确保模型加载、推理、界面等功能全部正常。
- 若有问题，查看 dist/LocalQA 下的文件结构，确认模型、配置等文件是否都在。

---

## 四、常见问题排查

1. **模型文件未被打包**：确认 spec 文件 datas 字段已包含模型目录。
2. **路径问题**：确认所有加载外部文件的地方都用 `resource_path`。
3. **依赖问题**：如有特殊依赖，需在 hiddenimports 中声明。

---

## 五、交付建议

- 提供完整的 dist/LocalQA 目录给客户，客户无需安装 Python 环境，直接运行 exe 即可。
- 附带 README，说明如何运行、注意事项等。

---

如需进一步协助（如帮你修改具体代码、生成 spec 文件等），请告知你模型加载的具体代码片段或遇到的报错信息，我可以帮你逐步定位和修复。

---

_**User**_

请帮我按照方案执行，直到打包完成

---

_**Assistant**_

<details>
            <summary>Searched codebase "加载模型" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/core/ai_model.py` | L179-238 |
| `src/core/ai_model.py` | L121-179 |
| `src/core/ai_model.py` | L238-262 |
| `src/core/ai_model.py` | L87-121 |
| `src/core/ai_model.py` | L1-32 |
| `src/core/ai_model.py` | L904-938 |
| `src/core/ai_model.py` | L262-315 |
| `src/core/ai_model.py` | L938-950 |
| `src/utils/exceptions.py` | L42-56 |
| `src/core/ai_model.py` | L32-87 |
| `src/utils/helpers.py` | L286-305 |
| `src/utils/config.py` | L1-54 |
| `src/core/ai_model.py` | L315-346 |
| `src/core/vector_store.py` | L116-151 |
| `models/shibing624/text2vec-base-chinese/modules.json` | L1-14 |
| `models/shibing624/text2vec-base-chinese/README.md` | L44-69 |
| `models/shibing624/text2vec-base-chinese/config.json` | L1-33 |
| `logs/app.log` | L1138-1153 |
| `models/shibing624/text2vec-base-chinese/1_Pooling/config.json` | L1-7 |
| `src/core/ai_model.py` | L449-476 |
| `logs/app.log` | L78-97 |
| `logs/app.log` | L930-949 |
| `config.yaml` | L1-50 |
| `models/shibing624/text2vec-base-chinese/README.md` | L69-120 |
| `logs/app.log` | L109-125 |

</details>

---

<details>
            <summary>Searched codebase "resource_path" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "def resource_path" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/LocalQA/src/utils/helpers.py` | L645 | `def resource_path(relative_path: str) -> str:` |

</details>

---

Read file: src/utils/helpers.py

---

```diff
      获取资源文件的绝对路径，兼容PyInstaller打包和开发环境。
      """
+     import sys, os
      if hasattr(sys, '_MEIPASS'):
          return os.path.join(sys._MEIPASS, relative_path)
+     # 若为打包环境，使用可执行文件所在目录
+     if getattr(sys, 'frozen', False):
+         return os.path.join(os.path.dirname(sys.executable), relative_path)
      return os.path.join(os.path.abspath("."), relative_path)
  
```

---

<details>
            <summary>Searched codebase "localqa.spec" • **0** results</summary>
        
No results found
</details>

---

```diff
- 
+ # -*- mode: python ; coding: utf-8 -*-
+ 
+ block_cipher = None
+ 
+ a = Analysis(
+     ['main.py'],
+     pathex=['.'],
+     binaries=[],
+     datas=[
+         ('models/*', 'models'),
+         ('config.yaml', '.'),
+         ('data/*', 'data'),
+         ('static/*', 'static'),
+     ],
+     hiddenimports=[],
+     hookspath=[],
+     runtime_hooks=[],
+     excludes=[],
+     win_no_prefer_redirects=False,
+     win_private_assemblies=False,
+     cipher=block_cipher,
+ )
+ 
+ pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
+ 
+ exe = EXE(
+     pyz,
+     a.scripts,
+     [],
+     exclude_binaries=True,
+     name='LocalQA',
+     debug=False,
+     bootloader_ignore_signals=False,
+     strip=False,
+     upx=True,
+     console=True,  # 如为GUI程序可改为False
+ )
+ 
+ coll = COLLECT(
+     exe,
+     a.binaries,
+     a.zipfiles,
+     a.datas,
+     strip=False,
+     upx=True,
+     upx_exclude=[],
+     name='LocalQA'
+ ) 
```

---

```bash
pyinstaller localqa.spec
```

```
PS D:\LocalQA> pyinstaller localqa.spec
167 INFO: PyInstaller: 6.14.1, contrib hooks: 2025.5
167 INFO: Python: 3.10.11
172 INFO: Platform: Windows-10-10.0.22631-SP0
172 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python310
174 INFO: Module search paths (PYTHONPATH):
['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Scripts\\pyinstaller.exe',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.zip',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib',
 'D:\\LocalQA']
272 WARNING: discover_hook_directories: Failed to process hook entry point 'EntryPoint(name='hook-dirs', val
ue='rapidfuzz.__pyinstaller:get_hook_dirs', group='pyinstaller40')': AttributeError: module 'rapidfuzz.__pyi
nstaller' has no attribute 'get_hook_dirs'
566 INFO: Appending 'datas' from .spec
626 INFO: checking Analysis
626 INFO: Building Analysis because Analysis-00.toc is non existent
626 INFO: Running Analysis Analysis-00.toc
626 INFO: Target bytecode optimization level: 0
626 INFO: Initializing module dependency graph...
627 INFO: Initializing module graph hook caches...
655 INFO: Analyzing modules for base_library.zip ...
1239 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
1288 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2177 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
3393 INFO: Caching module dependency graph...
3415 INFO: Looking for Python shared library...
3418 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python310.dll
3419 INFO: Analyzing D:\LocalQA\main.py
3448 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
3562 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
4242 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
4280 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'      
4282 INFO: SetuptoolsInfo: initializing cached setuptools info...
1003 WARNING: Failed to collect submodules for 'setuptools._vendor.packaging.licenses' because importing 'se
tuptools._vendor.packaging.licenses' raised: ModuleNotFoundError: No module named 'packaging.licenses'      
6292 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
6310 INFO: Processing standard module hook 'hook-torch.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
1169 WARNING: Failed to collect submodules for 'torch.utils.tensorboard' because importing 'torch.utils.tens
orboard' raised: ModuleNotFoundError: No module named 'tensorboard'
[2025-07-01 12:15:12,285] torch.distributed.elastic.multiprocessing.redirects: [WARNING] NOTE: Redirects are
 currently not supported in Windows or MacOs.
11544 INFO: hook-torch: this torch build does not depend on MKL...
11545 INFO: hook-torch: raising recursion limit to 5000
11557 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
11615 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
11932 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
11933 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptoo
ls._vendor.jaraco.text'!
11939 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
11960 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
11960 INFO: Processing pre-find-module-path hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
12132 INFO: Processing standard module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12151 INFO: Processing standard module hook 'hook-distutils.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12207 INFO: Processing standard module hook 'hook-_osx_support.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12274 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'        
12347 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'      
12348 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'set
uptools._vendor.jaraco.functools'!
12359 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppD
ata\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'    
12372 INFO: Processing standard module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12373 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
12652 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13134 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13274 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\
\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13276 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\App
Data\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'   
13281 INFO: Processing standard module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13337 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'        
13337 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setup
tools._vendor.jaraco.context'!
13347 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'     
13348 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'se
tuptools._vendor.backports.tarfile'!
13384 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13385 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13392 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13538 INFO: Processing standard module hook 'hook-numpy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
14047 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
14771 INFO: Processing standard module hook 'hook-psutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15911 INFO: Processing standard module hook 'hook-sympy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
16543 INFO: Processing standard module hook 'hook-matplotlib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
16853 INFO: Processing pre-safe-import-module hook 'hook-gi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16948 INFO: Processing standard module hook 'hook-matplotlib.backend_bases.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
17038 INFO: Processing standard module hook 'hook-PIL.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
17076 INFO: Processing standard module hook 'hook-PIL.Image.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
17394 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
17527 INFO: Processing standard module hook 'hook-PIL.ImageFilter.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
17990 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18260 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
18352 INFO: Processing standard module hook 'hook-matplotlib.pyplot.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
18936 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18981 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19863 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
19982 INFO: Processing standard module hook 'hook-py.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
20400 INFO: Processing standard module hook 'hook-pytest.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
20863 INFO: Processing standard module hook 'hook-pygments.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
27057 INFO: Processing standard module hook 'hook-scipy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
28794 INFO: Processing standard module hook 'hook-lxml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29071 INFO: Processing standard module hook 'hook-lxml.etree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29298 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29601 INFO: Processing standard module hook 'hook-scipy.linalg.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
30490 INFO: Processing standard module hook 'hook-scipy.sparse.csgraph.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
30622 INFO: Processing standard module hook 'hook-scipy.special._ufuncs.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
30716 INFO: Processing standard module hook 'hook-scipy.special._ellip_harm_2.py' from 'C:\\Users\\<USER>\\Ap
pData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
31498 INFO: Processing standard module hook 'hook-scipy.spatial.transform.rotation.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
31971 INFO: Processing standard module hook 'hook-scipy.stats._stats.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
35559 INFO: Processing standard module hook 'hook-xml.dom.domreg.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
36840 INFO: Processing standard module hook 'hook-pandas.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
37854 INFO: Processing standard module hook 'hook-pytz.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
38709 INFO: Processing standard module hook 'hook-pandas.io.formats.style.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
39719 INFO: Processing standard module hook 'hook-pyarrow.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40222 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40786 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40859 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
41556 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
41803 INFO: Processing standard module hook 'hook-bcrypt.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
41907 INFO: Processing standard module hook 'hook-fsspec.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
42350 INFO: Processing standard module hook 'hook-lz4.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
43143 INFO: Processing standard module hook 'hook-pandas.plotting.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
43778 INFO: Processing standard module hook 'hook-openpyxl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
45610 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
48074 INFO: Processing standard module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
48343 INFO: Processing standard module hook 'hook-pandas.io.clipboard.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
52589 INFO: Processing standard module hook 'hook-shelve.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
53490 INFO: Processing standard module hook 'hook-transformers.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
55123 INFO: Processing standard module hook 'hook-pydantic.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\experimental\__init__.py:7
: PydanticExperimentalWarning: This module is experimental, its contents are subject to change and deprecati
on.
  warnings.warn(
56801 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
58891 INFO: Processing standard module hook 'hook-anyio.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
59709 INFO: Processing standard module hook 'hook-orjson.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
61275 INFO: Processing standard module hook 'hook-websockets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
62844 INFO: Processing standard module hook 'hook-uvicorn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
64022 INFO: Processing standard module hook 'hook-bitsandbytes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
64122 INFO: Processing standard module hook 'hook-torchvision.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
64310 INFO: Processing standard module hook 'hook-torchvision.io.image.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
64392 INFO: Processing standard module hook 'hook-torchaudio.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
67814 INFO: Processing standard module hook 'hook-scipy.io.matlab.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
69027 INFO: Processing standard module hook 'hook-onnxruntime.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
69443 INFO: Processing pre-safe-import-module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'      
69510 INFO: Processing standard module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
69514 INFO: Processing standard module hook 'hook-pythoncom.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
69583 INFO: Processing standard module hook 'hook-pywintypes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
70406 INFO: Processing standard module hook 'hook-wcwidth.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
71084 INFO: Processing standard module hook 'hook-datasets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
71193 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
71194 INFO: TclTkInfo: initializing cached Tcl/Tk info...
71348 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
71775 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
72637 INFO: Processing standard module hook 'hook-regex.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
72699 INFO: Processing standard module hook 'hook-spacy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
76799 INFO: Processing standard module hook 'hook-thinc.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
78594 INFO: Processing standard module hook 'hook-srsly.msgpack._packer.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
78887 INFO: Processing pre-safe-import-module hook 'hook-tensorflow.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'    
78974 INFO: Processing standard module hook 'hook-thinc.backends.numpy_ops.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
79437 INFO: Processing standard module hook 'hook-langcodes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
81697 INFO: Processing standard module hook 'hook-jieba.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
82252 INFO: Processing standard module hook 'hook-astor.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
88560 INFO: Processing standard module hook 'hook-cv2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90374 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90419 INFO: Processing standard module hook 'hook-grpc.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
92897 INFO: Processing standard module hook 'hook-timm.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
95413 INFO: Processing standard module hook 'hook-emoji.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
100628 INFO: Processing standard module hook 'hook-nltk.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
102446 INFO: Processing standard module hook 'hook-sklearn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
102731 INFO: Processing standard module hook 'hook-sklearn.utils.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
102868 INFO: Processing standard module hook 'hook-sklearn.metrics.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
103969 INFO: Processing standard module hook 'hook-sklearn.metrics.cluster.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
104091 INFO: Processing standard module hook 'hook-sklearn.cluster.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
104126 INFO: Processing standard module hook 'hook-sklearn.metrics.pairwise.py' from 'C:\\Users\\<USER>\\AppD
ata\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
104602 INFO: Processing standard module hook 'hook-sklearn.neighbors.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
104679 INFO: Processing standard module hook 'hook-sklearn.linear_model.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
118915 INFO: Processing standard module hook 'hook-PyQt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
119329 INFO: Processing standard module hook 'hook-PyQt6.QtWidgets.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
119744 INFO: Processing standard module hook 'hook-PyQt6.QtCore.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
120167 INFO: Processing standard module hook 'hook-PyQt6.QtGui.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
122517 INFO: Processing standard module hook 'hook-opentelemetry.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
126261 INFO: Processing standard module hook 'hook-docx.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
126914 INFO: Processing standard module hook 'hook-pdfminer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
127253 INFO: Processing standard module hook 'hook-pptx.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
129657 INFO: Processing standard module hook 'hook-pypdfium2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
129682 INFO: Processing standard module hook 'hook-pypdfium2_raw.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
129833 INFO: Processing standard module hook 'hook-magic.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
129933 INFO: Processing standard module hook 'hook-iso639.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
129953 INFO: Processing standard module hook 'hook-langdetect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\whoosh\codec\whoosh3.py:1116: Synta
xWarning: "is" with a literal. Did you mean "=="?
  elif fixedsize is 0:
133240 INFO: Processing standard module hook 'hook-PyQt6.QtWebEngineWidgets.py' from 'C:\\Users\\<USER>\\AppD
ata\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
133362 INFO: Processing standard module hook 'hook-PyQt6.QtNetwork.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
133544 INFO: Processing standard module hook 'hook-PyQt6.QtPrintSupport.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
133621 INFO: Processing standard module hook 'hook-PyQt6.QtWebChannel.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
133710 INFO: Processing standard module hook 'hook-PyQt6.QtWebEngineCore.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
133845 INFO: Processing module hooks (post-graph stage)...
134217 INFO: Processing standard module hook 'hook-lxml.isoschematron.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
134751 INFO: Processing standard module hook 'hook-zope.interface.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
134848 INFO: Processing standard module hook 'hook-sklearn.tree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
142411 WARNING: Hidden import "importlib_resources.trees" not found!
142411 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
142412 INFO: Matplotlib backend selection method: automatic discovery of used backends
142418 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'sympy.testing.runtests'
: ['Agg', 'Agg']
142420 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'gradio.utils': ['agg']
142421 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'paddle.dataset.uci_hous
ing': ['Agg']
142422 INFO: The following Matplotlib backends were discovered by scanning for `matplotlib.use()` calls: ['A
gg', 'agg']. If your backend of choice is not in this list, either add a `matplotlib.use()` call to your cod
e, or configure the backend collection via hook options (see: https://pyinstaller.org/en/stable/hooks-config
.html#matplotlib-hooks).
142422 INFO: Selected matplotlib backends: ['Agg', 'agg']
142895 INFO: Processing standard module hook 'hook-PIL.SpiderImagePlugin.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
145055 INFO: Processing standard module hook 'hook-PyQt6.QtPositioning.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
145183 INFO: Processing standard module hook 'hook-PyQt6.QtQuick.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
145304 INFO: Processing standard module hook 'hook-PyQt6.QtQml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
146168 INFO: Processing standard module hook 'hook-PyQt6.QtOpenGL.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
146299 INFO: Processing standard module hook 'hook-PyQt6.QtQuickWidgets.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
146432 WARNING: Hidden import "scipy.special._cdflib" not found!
146801 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
147020 WARNING: Hidden import "pysqlite2" not found!
147021 WARNING: Hidden import "MySQLdb" not found!
147021 WARNING: Hidden import "psycopg2" not found!
147709 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
147710 INFO: Processing standard module hook 'hook-lxml.objectify.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
147711 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
148157 INFO: Performing binary vs. data reclassification (7919 entries)
154613 INFO: Looking for ctypes DLLs
154668 WARNING: Ignoring /usr/lib64/libgomp.so.1 imported from C:\Users\<USER>\AppData\Local\Programs\Python\
Python310\lib\site-packages\torch\_inductor\codecache.py - only basenames are supported with ctypes imports!
154749 INFO: Analyzing run-time hooks ...
154786 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154793 INFO: Including run-time hook 'pyi_rth_pyqt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Py
thon\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154799 INFO: Processing pre-find-module-path hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
154804 INFO: Processing standard module hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
154812 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154817 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154821 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154827 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
154830 INFO: Including run-time hook 'pyi_rth_mplconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154846 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
154850 INFO: Including run-time hook 'pyi_rth_nltk.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Pyt
hon\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
154854 INFO: Including run-time hook 'pyi_rth_pywintypes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
154857 INFO: Including run-time hook 'pyi_rth_pythoncom.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
154859 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
155094 INFO: Creating base_library.zip...
155154 INFO: Looking for dynamic libraries
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py:
227: UserWarning: The numpy.array_api submodule is still experimental. See NEP 47.
  __import__(package)
Could not find an up-to-date installation of `packaging`. License expressions might not be validated. To enf
orce validation, please install `packaging>=24.2`.
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py:
227: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resour
ces.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this pa
ckage or pin to Setuptools<81.
  __import__(package)
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\experimental\__init__.py:7
: PydanticExperimentalWarning: This module is experimental, its contents are subject to change and deprecati
on.
  warnings.warn(
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 287, in find_binary_dependencies
    child.call(import_library, package)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\isolated\_paren
t.py", line 322, in call
    raise SubprocessDiedError(
PyInstaller.isolated._parent.SubprocessDiedError: Child process died calling import_library() with args=('on
nx.reference',) and kwargs={}. Its exit code was 3221225477.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_ma
in
    return _run_code(code, main_globals, None,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\pyinstaller.exe\__main__.py", line 7,
 in <module>
    sys.exit(_console_script_run())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", l
ine 231, in _console_script_run
    run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", l
ine 215, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", l
ine 70, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 1282, in main
    build(specfile, distpath, workpath, clean_build)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 1220, in build
    exec(code, spec_namespace)
  File "localqa.spec", line 5, in <module>
    a = Analysis(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 584, in __init__
    self.__postinit__()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\datast
ruct.py", line 184, in __postinit__
    self.assemble()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 977, in assemble
    find_binary_dependencies(self.binaries, collected_packages, self.graph._bindepend_symlink_suppression)  
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 291, in find_binary_dependencies
    raise isolated.SubprocessDiedError(
PyInstaller.isolated._parent.SubprocessDiedError: Isolated subprocess crashed while importing package 'onnx.
reference'! Package import list: ['PyQt6', 'importlib', 'logging', 'importlib.metadata', 'urllib', 'email', 
'xmlrpc', 'xml.parsers', 'xml', 'http', 'xml.sax', 'ctypes', 'multiprocessing.dummy', 'nltk', 'xml.etree', '
nltk.tbl', 'nltk.misc', 'json', 'yaml', 'nltk.app', 'numpy.ma', 'numpy.testing', 'numpy.distutils', 'pydoc_d
ata', 'pyreadline3.console', 'ctypes.macholib', 'pyreadline3.logger', 'pyreadline3.keysyms', 'pyreadline3', 
'pyreadline3.modes', 'pyreadline3.lineeditor', 'pyreadline3.clipboard', 'numpy.core', 'numpy.compat', 'numpy
.lib', 'numpy.matrixlib', 'psutil', 'numpy.testing._private', 'unittest', 'asyncio', 'concurrent.futures', '
concurrent', 'numpy.array_api', 'numpy.typing', 'numpy._typing', 'numpy.linalg', 'numpy.random', 'numpy.fft'
, 'pyparsing', 'html', 'pyparsing.diagram', 'jinja2', 'markupsafe', 'packaging', 'contourpy', 'dateutil.tz',
 'dateutil.zoneinfo', 'dateutil.parser', 'dateutil', 'olefile', 'PIL', 'matplotlib.projections', 'mpl_toolki
ts.mplot3d', 'matplotlib.tri', 'matplotlib.backends', 'matplotlib.axes', 'kiwisolver', 'matplotlib.style', '
matplotlib._api', 'cycler', 'matplotlib', 'certifi', 'nltk.draw', 'regex', 'nltk.corpus.reader', 'sqlite3', 
'nltk.ccg', 'tkinter', 'nltk.cluster', 'numpy', 'numpy.polynomial', 'nltk.stem', 'nltk.sem', 'networkx', 'ne
tworkx.drawing', 'scipy', 'scipy._lib.array_api_compat.numpy', 'scipy._lib.array_api_compat', 'scipy._lib.ar
ray_api_compat.torch', 'torch', 'torch.utils.viz', 'torch.utils.model_dump', 'torch.utils.jit', 'torch.utils
.hipify', 'tabulate', 'wcwidth', 'dill', 'dbm', 'scipy.io', 'scipy.io.arff', 'scipy.io._harwell_boeing', 'sc
ipy.sparse', 'scipy.sparse.csgraph', 'scipy.sparse.linalg', 'scipy.linalg', 'scipy.special', 'scipy._lib.arr
ay_api_extra', 'scipy.fft', 'scipy.fft._pocketfft', 'scipy._lib._uarray', 'scipy.sparse.linalg._eigen', 'sci
py.sparse.linalg._eigen.lobpcg', 'scipy.sparse.linalg._eigen.arpack', 'scipy.sparse.linalg._dsolve', 'scipy.
sparse.linalg._isolve', 'scipy.io._fast_matrix_market', 'scipy.io.matlab', 'torchaudio', 'torchaudio.backend
', 'torchaudio._internal', 'torchaudio._backend', 'torchaudio.utils', 'torchaudio.transforms', 'torchaudio.s
ox_effects', 'torchaudio.pipelines', 'sentencepiece', 'torchaudio.pipelines._wav2vec2', 'torchaudio.pipeline
s._tts', 'torchaudio.models', 'torchaudio.models.wav2vec2', 'torchaudio.models.wav2vec2.utils', 'torchaudio.
models.squim', 'torchaudio.io', 'torchaudio.functional', 'torchaudio.datasets', 'torchaudio.compliance', 'to
rchaudio._extension', 'torchvision.io', 'torchvision', 'torchvision.ops', 'torchvision.models', 'torchvision
.models.video', 'torchvision.models.segmentation', 'torchvision.models.quantization', 'torchvision.models.op
tical_flow', 'torchvision.models.detection', 'torchvision.datasets', 'pycocotools', 'torchvision.transforms.
v2.functional', 'torchvision.transforms.v2', 'torchvision.tv_tensors', 'requests', 'idna', 'urllib3.util', '
zstandard', 'urllib3.http2', 'cryptography', 'cryptography.hazmat.bindings', 'cryptography.hazmat', 'cryptog
raphy.hazmat.bindings.openssl', 'cryptography.hazmat.decrepit.ciphers', 'cryptography.hazmat.decrepit', 'cry
ptography.hazmat.primitives.ciphers', 'cryptography.hazmat.primitives.asymmetric', 'cryptography.hazmat.prim
itives', 'cryptography.hazmat.primitives.serialization', 'bcrypt', 'cryptography.hazmat.backends.openssl', '
cryptography.hazmat.backends', 'cryptography.x509', 'urllib3.contrib', 'chardet', 'charset_normalizer', 'url
lib3', 'urllib3.contrib.emscripten', 'lmdb', 'cffi', 'distutils.command', 'distutils', 'setuptools._distutil
s', 'setuptools._distutils.compat', 'setuptools._vendor.jaraco.functools', 'more_itertools', 'setuptools', '
setuptools._distutils.command', '_distutils_hack', 'importlib_metadata', 'importlib_metadata.compat', 'zipp'
, 'zipp.compat', 'wheel', 'setuptools._vendor.jaraco.text', 'setuptools._vendor.backports.tarfile', 'setupto
ols._vendor.backports', 'setuptools._vendor.backports.tarfile.compat', 'backports', 'importlib_resources', '
importlib_resources.future', 'importlib_resources.compat', 'setuptools.compat', 'wheel.cli', 'wheel.vendored
.packaging', 'wheel.vendored', 'setuptools.config._validate_pyproject', 'setuptools._vendor.packaging', 'tom
li', 'setuptools.config', 'setuptools.command', 'pycparser', 'pycparser.ply', 'torchvision.transforms', 'tor
ch.utils.data.datapipes.utils', 'torch.utils.data.datapipes.map', 'torch.utils.data.datapipes.iter', 'pandas
', 'pandas.core.dtypes', 'pandas.core', 'pandas.core.indexers', 'pandas.core.groupby', 'pandas.core._numba.k
ernels', 'pandas.core.window', 'pandas.util.version', 'pandas.util', 'pandas.core.util', 'pandas.core.comput
ation', 'pandas.core.array_algos', 'pyarrow.vendored', 'pyarrow', 'pyarrow.parquet', 'pyarrow.interchange', 
'Cython', 'Cython.Build', 'Cython.Distutils', 'pyximport', 'Cython.Plex', 'pygments.lexers', 'pygments', 'py
gments.styles', 'pygments.filters', 'pygments.formatters', 'Cython.Tempita', 'pkg_resources', 'platformdirs'
, 'Cython.Compiler', 'lxml', 'lxml.isoschematron', 'lxml.includes.libxslt', 'lxml.includes.libxml', 'lxml.in
cludes.libexslt', 'lxml.includes.extlibs', 'lxml.includes', 'bs4', 'soupsieve', 'bs4.builder', 'html5lib.tre
ebuilders', 'xml.dom', 'html5lib', 'html5lib.filters', 'html5lib.treewalkers', 'html5lib._trie', 'webencodin
gs', 'lxml.html', 'cssselect', 'Cython.Debugger', 'pyarrow.tests', 'pytest', 'zope.interface', 'pluggy', 'in
iconfig', '_pytest._py', '_pytest._io', 'colorama', 'exceptiongroup', '_pytest.mark', '_pytest.config', '_py
test.assertion', '_pytest._code', '_pytest', 'fsspec', 'yarl', 'propcache', 'multidict', 'aiohttp', 'attr', 
'frozenlist', 'aiosignal', 'async_timeout', 'aiohttp._websocket', 'aiohappyeyeballs', 'fsspec.implementation
s', 'lz4.frame', 'lz4', 'tqdm', 'pandas.core.tools', 'pandas.core.arrays.arrow', 'pandas.core.strings', 'pan
das.core._numba', 'pandas.core.ops', 'pandas.compat.numpy', 'pandas.core.reshape', 'scipy.interpolate', 'sci
py.optimize', 'scipy.optimize._highspy', 'scipy.optimize._shgo_lib', 'scipy.spatial', 'scipy.spatial.transfo
rm', 'scipy.constants', 'scipy.optimize._lsq', 'scipy._lib.cobyqa', 'scipy._lib.cobyqa.utils', 'scipy._lib.c
obyqa.subsolvers', 'scipy.optimize._trustregion_constr', 'scipy.optimize._trlib', 'pandas.core.indexes', 'pa
ndas.io.formats', 'pandas.io.excel', 'xlsxwriter', 'openpyxl.cell', 'openpyxl.xml', 'et_xmlfile', 'openpyxl.
descriptors', 'openpyxl.worksheet', 'openpyxl.utils', 'openpyxl.formula', 'openpyxl.compat', 'openpyxl.style
s', 'openpyxl.workbook', 'openpyxl.packaging', 'openpyxl.chartsheet', 'openpyxl.drawing', 'openpyxl.chart', 
'openpyxl.writer', 'openpyxl.comments', 'openpyxl.formatting', 'openpyxl', 'openpyxl.reader', 'openpyxl.pivo
t', 'openpyxl.workbook.external_link', 'xlrd', 'pandas.io.parsers', 'pandas.api.types', 'pytz', 'pandas.core
.arrays', 'pandas.core.arrays.sparse', 'pandas.core.methods', 'pandas.core.internals', 'pandas.io.clipboard'
, 'sqlalchemy.engine', 'sqlalchemy.cyextension', 'sqlalchemy.event', 'greenlet', 'sqlalchemy.util', 'sqlalch
emy.future', 'sqlalchemy.pool', 'sqlalchemy.ext.asyncio', 'sqlalchemy.ext', 'sqlalchemy.orm', 'sqlalchemy.di
alects', 'sqlalchemy.sql', 'sqlalchemy', 'sqlalchemy.dialects.sqlite', 'sqlalchemy.dialects.postgresql', 'sq
lalchemy.dialects.oracle', 'sqlalchemy.dialects.mysql', 'sqlalchemy.connectors', 'sqlalchemy.dialects.mssql'
, 'pandas.io.json', 'google.auth', 'google.auth.transport', 'google.oauth2', 'google.auth.compute_engine', '
cachetools', 'google.auth.crypt', 'rsa', 'pyasn1', 'pyasn1.compat', 'pyasn1.codec.ber', 'pyasn1.codec', 'pya
sn1.type', 'pyasn1.codec.cer', 'pyasn1_modules', 'pyasn1.codec.der', 'pandas.core.interchange', 'pandas._lib
s.window', 'pandas._libs.tslibs', 'pandas.io.sas', 'pandas._libs', 'pandas._testing', 'pandas.plotting', 'pa
ndas.plotting._matplotlib', 'pandas.io', 'pandas.errors', 'pandas.arrays', 'pandas.api', 'pandas.api.typing'
, 'pandas.api.interchange', 'pandas.api.indexers', 'pandas.api.extensions', 'pandas.tseries', 'pandas._confi
g', 'pandas.compat', 'torch.utils.data.datapipes.dataframe', 'torch.utils.data.datapipes', 'torch.utils.data
._utils', 'torch.utils.bottleneck', 'torch.utils.benchmark.utils.valgrind_wrapper', 'torch.utils.benchmark.u
tils', 'torch.utils.benchmark.op_fuzzers', 'torch.utils.benchmark.examples', 'torch.utils.benchmark', 'sympy
.solvers', 'sympy.multipledispatch', 'sympy.matrices.expressions', 'sympy.functions.special', 'mpmath.libmp'
, 'mpmath', 'mpmath.tests', 'mpmath.matrices', 'mpmath.calculus', 'mpmath.functions', 'sympy.external', 'sym
py.categories', 'sympy.sets', 'sympy.sets.handlers', 'sympy.physics.control', 'sympy.plotting', 'sympy.plott
ing.pygletplot', 'sympy.polys.domains', 'sympy.polys.agca', 'sympy.polys.numberfields', 'sympy.polys.matrice
s', 'sympy.codegen', 'sympy.tensor.array.expressions', 'sympy.tensor', 'sympy.parsing', 'sympy.utilities', '
sympy.strategies.branch', 'sympy.strategies', 'sympy.plotting.backends.textbackend', 'sympy.plotting.backend
s', 'sympy.plotting.backends.matplotlibbackend', 'sympy.plotting.intervalmath', 'sympy.concrete', 'sympy.phy
sics', 'sympy.physics.units', 'sympy.physics.units.systems', 'sympy.physics.units.definitions', 'sympy.funct
ions.combinatorial', 'sympy.vector', 'sympy.tensor.array', 'sympy.printing', 'sympy.utilities.mathml', 'symp
y.assumptions.relation', 'sympy.printing.pretty', 'sympy.ntheory', 'sympy.combinatorics', 'sympy.discrete', 
'sympy.testing', 'sympy.interactive', 'sympy.solvers.ode', 'sympy.solvers.diophantine', 'sympy.series', 'sym
py.calculus', 'sympy.simplify', 'sympy.geometry', 'sympy.polys', 'sympy.matrices', 'sympy.functions', 'sympy
.integrals', 'sympy.functions.elementary', 'sympy.core', 'sympy.logic.algorithms', 'sympy.assumptions.predic
ates', 'sympy.assumptions.handlers', 'sympy.assumptions', 'sympy.logic', 'sympy', 'sympy.algebras', 'torch.u
tils._sympy', 'torch.utils', 'torch.testing._internal.test_module', 'torch.testing._internal.generated', 'to
rch.testing._internal.distributed.rpc.jit', 'torch.testing._internal.distributed.rpc.examples', 'torch.testi
ng._internal.distributed.rpc', 'torch.testing._internal.distributed.pipeline', 'torch.distributed.pipeline.s
ync', 'torch.distributed.pipeline.sync.skip', 'torch.testing._internal.distributed.nn.api', 'torch.testing._
internal.distributed.nn', 'torch.testing._internal.distributed._tensor', 'torch.testing._internal.distribute
d._shard', 'torch.testing._internal.distributed', 'torch.testing._internal.data', 'torch.testing._internal.o
pinfo', 'torch.testing._internal.opinfo.definitions', 'scipy.signal', 'scipy.ndimage', 'scipy.signal.windows
', 'torchgen', 'functorch.experimental', 'functorch', 'transformers', 'safetensors', 'huggingface_hub.utils'
, 'pydantic', 'toml', 'dotenv', 'pydantic.v1', 'pydantic.plugin', 'annotated_types', 'pydantic.experimental'
, 'pydantic.deprecated', 'typing_inspection', 'zoneinfo', 'tzdata', 'tzdata.zoneinfo.US', 'tzdata.zoneinfo.P
acific', 'tzdata.zoneinfo.Mexico', 'tzdata.zoneinfo.Indian', 'tzdata.zoneinfo.Europe', 'tzdata.zoneinfo.Etc'
, 'tzdata.zoneinfo.Chile', 'tzdata.zoneinfo.Canada', 'tzdata.zoneinfo.Brazil', 'tzdata.zoneinfo.Australia', 
'tzdata.zoneinfo.Atlantic', 'tzdata.zoneinfo.Asia', 'tzdata.zoneinfo.Arctic', 'tzdata.zoneinfo.Antarctica', 
'tzdata.zoneinfo.America.North_Dakota', 'tzdata.zoneinfo.America.Kentucky', 'tzdata.zoneinfo.America.Indiana
', 'tzdata.zoneinfo.America.Argentina', 'tzdata.zoneinfo.America', 'tzdata.zoneinfo.Africa', 'tzdata.zoneinf
o', 'rich', 'markdown_it', 'markdown_it.rules_core', 'markdown_it.rules_inline', 'mdurl', 'markdown_it.commo
n', 'markdown_it.rules_block', 'markdown_it.presets', 'markdown_it.helpers', 'pydantic._internal', 'pydantic
_core', 'filelock', 'huggingface_hub.commands', 'huggingface_hub', 'huggingface_hub.serialization', 'tqdm.co
ntrib', 'huggingface_hub.inference._mcp', 'huggingface_hub.inference', 'huggingface_hub.inference._providers
', 'huggingface_hub.inference._generated.types', 'huggingface_hub.inference._generated', 'orjson', 'starlett
e', 'multipart', 'python_multipart', 'starlette.middleware', 'anyio.abc', 'anyio._core', 'sniffio', 'anyio.s
treams', 'anyio', 'anyio._backends', 'fastapi', 'fastapi.openapi', 'fastapi.dependencies', 'fastapi.security
', 'gradio', 'gradio.cli', 'gradio.cli.commands', 'typer', 'click', 'shellingham', 'tomlkit', 'semantic_vers
ion', 'gradio.cli.commands.components', 'gradio_client', 'websockets', 'websockets.sync', 'websockets.legacy
', 'websockets.extensions', 'httpx', 'httpcore', 'httpcore._backends', 'httpcore._sync', 'h11', 'httpcore._a
sync', 'httpx._transports', 'gradio_client.cli', 'openai', 'openai.resources.vector_stores', 'openai.resourc
es', 'openai.resources.responses', 'openai.types.responses', 'openai.types.shared_params', 'openai.types.sha
red', 'openai.types.chat', 'openai.resources.fine_tuning', 'openai.resources.fine_tuning.checkpoints', 'open
ai.types.fine_tuning.checkpoints', 'openai.types.fine_tuning', 'openai.types.graders', 'openai.resources.fin
e_tuning.alpha', 'openai.types.fine_tuning.alpha', 'openai.resources.fine_tuning.jobs', 'openai.types.fine_t
uning.jobs', 'openai.resources.containers', 'openai.resources.containers.files', 'openai.types.containers', 
'openai.resources.uploads', 'openai.types.uploads', 'openai.resources.evals', 'openai.resources.evals.runs',
 'openai.types.evals', 'openai.types.evals.runs', 'openai.resources.audio', 'openai.types.audio', 'openai.re
sources.chat', 'openai.resources.chat.completions', 'openai.lib.streaming.chat', 'jiter', 'openai.lib._parsi
ng', 'openai.types.chat.completions', 'openai.resources.beta', 'openai.types.beta', 'openai.types.beta.threa
ds', 'openai.types.beta.threads.runs', 'openai.resources.beta.threads', 'openai.resources.beta.threads.runs'
, 'openai.types.vector_stores', 'openai.lib.streaming.responses', 'openai._extras', 'openai.resources.beta.r
ealtime', 'openai.types.beta.realtime', 'openai.types.webhooks', 'openai.lib.streaming', 'openai.lib', 'dist
ro', 'openai._utils', 'openai.types', 'pydub', 'groovy', 'gradio.layouts', 'gradio.components', 'ffmpy', 'gr
adio.sketch', 'gradio._simple_templates', 'uvicorn.middleware', 'uvicorn', 'watchfiles', 'uvicorn.supervisor
s', 'uvicorn.protocols.websockets', 'httptools', 'httptools.parser', 'uvicorn.protocols.http', 'uvicorn.prot
ocols', 'uvicorn.loops', 'uvicorn.lifespan', 'gradio.themes', 'gradio.themes.utils', 'itsdangerous', 'safeht
tpx', 'aiofiles', 'aiofiles.tempfile', 'aiofiles.threadpool', 'torch.utils.tensorboard', 'google.protobuf.py
ext', 'google.protobuf.internal', 'google.protobuf', 'onnx', 'onnx.reference', 'onnx.reference.ops.aionnxml'
, 'onnx.reference.ops.experimental', 'onnx.reference.ops.aionnx_preview_training', 'onnx.reference.ops', 'on
nx.reference.ops_optimized', 'onnx.defs', 'datasets', 'datasets.utils', 'Crypto.Random', 'Crypto', 'Crypto.U
til', 'Crypto.Cipher', 'Crypto.Hash', 'Crypto.Protocol', 'datasets.filesystems', 'tiktoken', 'spacy', 'wasab
i', 'thinc', 'thinc.tests.shims', 'thinc.tests.regression.issue519', 'thinc.tests.regression', 'thinc.tests.
mypy.modules', 'thinc.tests.mypy', 'thinc.tests.model', 'thinc.tests.layers', 'thinc.tests.extra', 'thinc.te
sts.backends', 'thinc.tests', 'thinc.shims', 'murmurhash', 'thinc.layers', 'thinc.extra.tests', 'thinc.extra
', 'blis', 'preshed', 'cymem', 'thinc.backends', 'confection', 'catalogue', 'catalogue._importlib_metadata',
 'srsly', 'srsly.ruamel_yaml', 'srsly.cloudpickle', 'srsly.msgpack', 'srsly.ujson', 'spacy.training.converte
rs', 'spacy.training', 'spacy.tokens', 'spacy.tests.vocab_vectors', 'spacy.tests.training', 'spacy.tests.tok
enizer', 'wsgiref', 'spacy.tests.serialize', 'spacy.tests.pipeline', 'spacy.tests.parser', 'spacy.tests.pack
age', 'spacy.tests.morphology', 'spacy.tests.matcher', 'spacy.tests.lang.zh', 'spacy.tests.lang.yo', 'spacy.
tests.lang.xx', 'spacy.tests.lang.vi', 'spacy.tests.lang.ur', 'spacy.tests.lang.uk', 'spacy.tests.lang.tt', 
'spacy.tests.lang.tr', 'spacy.tests.lang.tl', 'spacy.tests.lang.ti', 'spacy.tests.lang.th', 'spacy.tests.lan
g.ta', 'spacy.tests.lang.sv', 'spacy.tests.lang.sr', 'spacy.tests.lang.sq', 'spacy.tests.lang.sl', 'spacy.te
sts.lang.sk', 'spacy.tests.lang.sa', 'spacy.tests.lang.ru', 'spacy.tests.lang.ro', 'spacy.tests.lang.pt', 's
pacy.tests.lang.pl', 'spacy.tests.lang.nn', 'spacy.tests.lang.nl', 'spacy.tests.lang.ne', 'spacy.tests.lang.
nb', 'spacy.tests.lang.ms', 'spacy.tests.lang.ml', 'spacy.tests.lang.mk', 'spacy.tests.lang.lv', 'spacy.test
s.lang.lt', 'spacy.tests.lang.lg', 'spacy.tests.lang.lb', 'spacy.tests.lang.la', 'spacy.tests.lang.ky', 'spa
cy.tests.lang.ko', 'spacy.tests.lang.kmr', 'spacy.tests.lang.ja', 'spacy.tests.lang.it', 'spacy.tests.lang.i
s', 'spacy.tests.lang.id', 'spacy.tests.lang.hy', 'spacy.tests.lang.hu', 'spacy.tests.lang.hsb', 'spacy.test
s.lang.hr', 'spacy.tests.lang.hi', 'spacy.tests.lang.he', 'spacy.tests.lang.gu', 'spacy.tests.lang.grc', 'sp
acy.tests.lang.ga', 'spacy.tests.lang.fr', 'spacy.tests.lang.fo', 'spacy.tests.lang.fi', 'spacy.tests.lang.f
a', 'spacy.tests.lang.eu', 'spacy.tests.lang.et', 'spacy.tests.lang.es', 'spacy.tests.lang.en', 'spacy.tests
.lang.el', 'spacy.tests.lang.dsb', 'spacy.tests.lang.de', 'spacy.tests.lang.da', 'spacy.tests.lang.cs', 'spa
cy.tests.lang.ca', 'spacy.tests.lang.bo', 'spacy.tests.lang.bn', 'spacy.tests.lang.ar', 'spacy.tests.lang.am
', 'spacy.tests.lang.af', 'spacy.tests.lang', 'spacy.tests.doc', 'spacy.tests', 'spacy.pipeline.legacy', 'sp
acy.pipeline._parser_internals', 'spacy.pipeline._edit_tree_internals', 'spacy.ml.models', 'spacy.ml', 'spac
y.matcher', 'spacy.lang.zh', 'jieba', 'jieba.lac_small', 'paddle.static', 'paddle.nn.initializer', 'paddle.n
n', 'paddle.utils', 'paddle.tensor', 'opt_einsum', 'opt_einsum.backends', 'paddle.framework', 'paddle.fluid.
proto', 'paddle.jit.dy2static', 'astor', 'paddle.jit', 'paddle.optimizer', 'paddle.incubate.autograd', 'padd
le.incubate', 'paddle.incubate.xpu', 'paddle.distributed.fleet', 'paddle.distributed.auto_parallel', 'paddle
.distributed.passes', 'paddle.distributed.ps.utils', 'paddle.distributed.ps', 'paddle.distributed.transpiler
', 'paddle.distributed.transpiler.details', 'paddle.incubate.distributed.fleet.parameter_server.ir', 'paddle
.incubate.distributed.fleet.parameter_server', 'paddle.amp', 'paddle.incubate.distributed.fleet', 'paddle.di
stributed.communication', 'paddle.distributed.communication.stream', 'paddle.distributed.fleet.meta_parallel
.pp_utils', 'paddle.distributed.fleet.meta_parallel', 'paddle.distributed.fleet.meta_optimizers.dygraph_opti
mizer', 'paddle.distributed.fleet.meta_optimizers', 'paddle.distributed.fleet.meta_optimizers.sharding', 'pa
ddle.distributed.fleet.meta_parallel.parallel_layers', 'paddle.incubate.nn.functional', 'paddle.distributed.
fleet.base', 'paddle.distributed.fleet.layers.mpu', 'paddle.distributed.fleet.recompute', 'paddle.incubate.d
istributed', 'paddle.static.quantization', 'paddle.io', 'paddle.io.dataloader', 'paddle.profiler', 'paddle.s
tatic.amp.bf16', 'paddle.cost_model', 'paddle.distributed.auto_parallel.static.operators', 'paddle.distribut
ed.auto_parallel.static.cost', 'paddle.distributed.auto_parallel.static', 'paddle.distributed.utils', 'paddl
e.distributed.auto_parallel.static.tuner', 'paddle.hapi', 'paddle.metric', 'paddle.distributed.fleet.utils',
 'paddle.distributed.fleet.metrics', 'paddle.distributed.fleet.data_generator', 'paddle.distributed.fleet.da
taset', 'paddle.distributed.fleet.proto', 'paddle.distributed.fleet.runtime', 'paddle.incubate.distributed.f
leet.parameter_server.distribute_transpiler', 'paddle.incubate.distributed.fleet.parameter_server.pslib', 'p
addle.distributed', 'paddle.distributed.rpc', 'paddle.distributed.launch.context', 'paddle.device', 'paddle.
device.xpu', 'paddle.device.cuda', 'paddle.distributed.launch.plugins', 'paddle.distributed.launch', 'paddle
.distributed.launch.controllers', 'paddle.distributed.launch.utils', 'paddle.distributed.launch.job', 'paddl
e.distributed.sharding', 'paddle.distributed.fleet.meta_parallel.sharding', 'paddle.distributed.fleet.elasti
c', 'paddle.incubate.layers', 'paddle.incubate.nn', 'paddle.incubate.multiprocessing', 'paddle.incubate.asp'
, 'paddle.incubate.passes', 'paddle.incubate.tensor', 'paddle.incubate.operators', 'paddle.incubate.checkpoi
nt', 'paddle.fluid.incubate.checkpoint', 'paddle.fluid.incubate', 'paddle.incubate.optimizer', 'paddle.incub
ate.optimizer.functional', 'paddle.autograd', 'paddle.reader', 'paddle.utils.cpp_extension', 'paddle.utils.g
ast', 'paddle.nn.quant', 'paddle.nn.quant.qat', 'paddle.nn.utils', 'paddle.nn.layer', 'paddle.nn.functional'
, 'paddle.fluid.layers', 'proto', 'proto.marshal', 'proto.marshal.rules', 'proto.marshal.collections', 'padd
le.static.nn', 'paddle.static.amp', 'paddle.fluid.dygraph', 'paddle.version', 'paddle', 'paddle.text', 'padd
le.text.datasets', 'paddle.quantization', 'paddle.quantization.imperative', 'paddle.sparse', 'paddle.sparse.
nn', 'paddle.sparse.nn.functional', 'paddle.geometric', 'paddle.geometric.sampling', 'paddle.geometric.messa
ge_passing', 'paddle.audio', 'paddle.audio.backends', 'paddle.audio.datasets', 'paddle.audio.functional', 'p
addle.audio.features', 'paddle.vision', 'paddle.vision.datasets', 'paddle.vision.transforms', 'paddle.vision
.models', 'paddle.onnx', 'paddle.inference', 'paddle.dataset', 'cv2', 'cv2.utils', 'cv2.typing', 'cv2.misc',
 'cv2.mat_wrapper', 'cv2.gapi', 'cv2.data', 'paddle.distribution', 'paddle.fluid', 'paddle.fluid.contrib', '
paddle.fluid.contrib.extend_optimizer', 'jieba.finalseg', 'spacy.lang.yo', 'spacy.lang.xx', 'spacy.lang.vi',
 'spacy.lang.ur', 'spacy.lang.uk', 'spacy.lang.tt', 'spacy.lang.tr', 'spacy.lang.tn', 'spacy.lang.tl', 'spac
y.lang.ti', 'spacy.lang.th', 'spacy.lang.te', 'spacy.lang.ta', 'spacy.lang.sv', 'spacy.lang.sr', 'spacy.lang
.sq', 'spacy.lang.sl', 'spacy.lang.sk', 'spacy.lang.si', 'spacy.lang.sa', 'spacy.lang.ru', 'spacy.lang.ro', 
'spacy.lang.pt', 'spacy.lang.pl', 'spacy.lang.nn', 'spacy.lang.nl', 'spacy.lang.ne', 'spacy.lang.nb', 'spacy
.lang.ms', 'spacy.lang.mr', 'spacy.lang.ml', 'spacy.lang.mk', 'spacy.lang.lv', 'spacy.lang.lt', 'spacy.lang.
lij', 'spacy.lang.lg', 'spacy.lang.lb', 'spacy.lang.la', 'spacy.lang.ky', 'spacy.lang.ko', 'spacy.lang.kn', 
'spacy.lang.kmr', 'spacy.lang.ja', 'spacy.lang.it', 'spacy.lang.is', 'spacy.lang.id', 'spacy.lang.hy', 'spac
y.lang.hu', 'spacy.lang.hsb', 'spacy.lang.hr', 'spacy.lang.hi', 'spacy.lang.he', 'spacy.lang.gu', 'spacy.lan
g.grc', 'spacy.lang.gd', 'spacy.lang.ga', 'spacy.lang.fr', 'spacy.lang.fo', 'spacy.lang.fi', 'spacy.lang.fa'
, 'spacy.lang.eu', 'spacy.lang.et', 'spacy.lang.es', 'spacy.lang.en', 'spacy.lang.el', 'spacy.lang.dsb', 'sp
acy.lang.de', 'spacy.lang.da', 'spacy.lang.cs', 'spacy.lang.ca', 'spacy.lang.bo', 'spacy.lang.bn', 'spacy.la
ng.bg', 'spacy.lang.az', 'spacy.lang.ar', 'spacy.lang.am', 'spacy.lang.af', 'spacy.lang', 'spacy.kb', 'wease
l.cli', 'cloudpathlib', 'cloudpathlib.s3', 'google.api_core.retry', 'grpc.experimental.aio', 'grpc.aio', 'gr
pc._cython', 'grpc.experimental', 'grpc_status', 'grpc', 'google.api_core', 'cloudpathlib.gs', 'cloudpathlib
.azure', 'weasel', 'weasel.util', 'smart_open', 'wrapt', 'spacy.cli.project', 'spacy.cli', 'spacy.displacy',
 'spacy.pipeline', 'langcodes', 'language_data', 'datasets.parallel', 'joblib', 'joblib.externals.loky', 'jo
blib.externals.loky.backend', 'joblib.externals.cloudpickle', 'joblib.externals', 'multiprocess.dummy', '_mu
ltiprocess', 'test.support', 'test', 'multiprocess', 'pyarrow_hotfix', 'datasets.tasks', 'datasets.packaged_
modules', 'cbor', 'datasets.packaged_modules.webdataset', 'datasets.packaged_modules.text', 'datasets.packag
ed_modules.sql', 'datasets.packaged_modules.parquet', 'datasets.packaged_modules.pandas', 'datasets.packaged
_modules.json', 'datasets.packaged_modules.folder_based_builder', 'datasets.packaged_modules.imagefolder', '
datasets.packaged_modules.csv', 'datasets.packaged_modules.cache', 'datasets.packaged_modules.audiofolder', 
'datasets.packaged_modules.arrow', 'datasets.io', 'datasets.packaged_modules.spark', 'datasets.packaged_modu
les.generator', 'datasets.formatting', 'xxhash', 'datasets.features', 'datasets.download', 'faiss', 'transfo
rmers.benchmark', 'accelerate.utils', 'accelerate.commands.config', 'accelerate.commands', 'accelerate.comma
nds.menu', 'peft', 'peft.utils', 'auto_gptq.utils', 'auto_gptq', 'auto_gptq.nn_modules.qlinear', 'auto_gptq.
nn_modules', 'auto_gptq.quantization', 'auto_gptq.nn_modules.triton_utils', 'peft.tuners.lora', 'bitsandbyte
s', 'bitsandbytes.optim', 'bitsandbytes.nn', 'bitsandbytes.triton', 'bitsandbytes.autograd', 'bitsandbytes.r
esearch', 'bitsandbytes.research.autograd', 'bitsandbytes.research.nn', 'peft.tuners.adalora', 'auto_gptq.mo
deling', 'transformers.onnx', 'onnxruntime', 'onnxruntime.capi.training', 'onnxruntime.capi', 'gekko', 'flas
k', 'werkzeug.sansio', 'werkzeug', 'watchdog.observers', 'watchdog', 'watchdog.utils', 'werkzeug.debug', 'we
rkzeug.middleware', 'werkzeug.datastructures', 'werkzeug.routing', 'werkzeug.wrappers', 'blinker', 'asgiref'
, 'flask.json', 'peft.tuners.trainable_tokens', 'peft.tuners', 'peft.tuners.xlora', 'peft.tuners.vera', 'pef
t.tuners.vblora', 'peft.tuners.prompt_tuning', 'peft.tuners.prefix_tuning', 'peft.tuners.poly', 'peft.tuners
.p_tuning', 'peft.tuners.multitask_prompt_tuning', 'peft.tuners.mixed', 'peft.tuners.oft', 'peft.tuners.lokr
', 'peft.tuners.loha', 'peft.tuners.ln_tuning', 'peft.tuners.ia3', 'peft.tuners.hra', 'peft.tuners.fourierft
', 'peft.tuners.cpt', 'peft.tuners.bone', 'peft.tuners.boft', 'peft.tuners.adaption_prompt', 'accelerate', '
optimum.bettertransformer', 'optimum.utils', 'optimum.bettertransformer.models', 'transformers.quantizers', 
'optimum.gptq', 'transformers.data.datasets', 'transformers.data.processors', 'tokenizers.models', 'tokenize
rs.processors', 'tokenizers.pre_tokenizers', 'tokenizers.normalizers', 'tokenizers.decoders', 'tokenizers', 
'tokenizers.implementations', 'tokenizers.trainers', 'transformers.models.mbart50', 'transformers.models', '
transformers.models.megatron_gpt2', 'transformers.models.dit', 'transformers.models.dialogpt', 'transformers
.models.deprecated', 'transformers.models.nllb', 'transformers.models.mluke', 'transformers.models.gpt_sw3',
 'transformers.models.cpm', 'transformers.models.code_llama', 'transformers.models.bartpho', 'transformers.m
odels.barthez', 'transformers.models.zoedepth', 'transformers.models.yoso', 'transformers.models.yolos', 'tr
ansformers.models.xmod', 'transformers.models.xlnet', 'transformers.models.xlm_roberta_xl', 'transformers.mo
dels.xlm_roberta', 'transformers.models.xlm', 'transformers.models.xglm', 'transformers.models.x_clip', 'tra
nsformers.models.whisper', 'transformers.models.wavlm', 'transformers.models.wav2vec2_with_lm', 'transformer
s.models.wav2vec2_phoneme', 'transformers.models.wav2vec2_conformer', 'transformers.models.wav2vec2_bert', '
transformers.models.wav2vec2', 'transformers.models.vivit', 'transformers.models.vits', 'transformers.models
.vitmatte', 'transformers.models.vitdet', 'transformers.models.vit_msn', 'transformers.models.vit_mae', 'tra
nsformers.models.vit', 'transformers.models.visual_bert', 'transformers.models.vision_text_dual_encoder', 't
ransformers.models.vision_encoder_decoder', 'transformers.models.vipllava', 'transformers.models.vilt', 'tra
nsformers.models.videomae', 'transformers.models.video_llava', 'transformers.models.upernet', 'transformers.
models.univnet', 'transformers.models.unispeech_sat', 'transformers.models.unispeech', 'transformers.models.
umt5', 'transformers.models.udop', 'transformers.models.tvp', 'transformers.models.trocr', 'transformers.mod
els.timm_backbone', 'timm', 'timm.models', 'timm.data', 'timm.data.readers', 'timm.utils', 'timm.layers', 't
ransformers.models.timesformer', 'transformers.models.time_series_transformer', 'transformers.models.tapas',
 'transformers.models.table_transformer', 'transformers.models.t5', 'transformers.models.switch_transformers
', 'transformers.models.swinv2', 'transformers.models.swin2sr', 'transformers.models.swin', 'transformers.mo
dels.swiftformer', 'transformers.models.superpoint', 'transformers.models.starcoder2', 'transformers.models.
stablelm', 'transformers.models.squeezebert', 'transformers.models.splinter', 'transformers.models.speecht5'
, 'transformers.models.speech_to_text', 'transformers.models.speech_encoder_decoder', 'transformers.models.s
iglip', 'transformers.models.sew_d', 'transformers.models.sew', 'transformers.models.seggpt', 'transformers.
models.segformer', 'transformers.models.seamless_m4t_v2', 'transformers.models.seamless_m4t', 'transformers.
models.sam', 'transformers.models.rwkv', 'transformers.models.rt_detr', 'transformers.models.roformer', 'tra
nsformers.models.roc_bert', 'transformers.models.roberta_prelayernorm', 'transformers.models.roberta', 'tran
sformers.models.resnet', 'transformers.models.rembert', 'transformers.models.regnet', 'transformers.models.r
eformer', 'transformers.models.recurrent_gemma', 'transformers.models.rag', 'transformers.models.qwen2_moe',
 'transformers.models.qwen2', 'transformers.models.pvt_v2', 'transformers.models.pvt', 'transformers.models.
prophetnet', 'transformers.models.pop2piano', 'transformers.models.poolformer', 'transformers.models.plbart'
, 'transformers.models.pix2struct', 'transformers.models.phobert', 'transformers.models.phi3', 'transformers
.models.phi', 'transformers.models.persimmon', 'transformers.models.perceiver', 'transformers.models.pegasus
_x', 'transformers.models.pegasus', 'transformers.models.patchtst', 'transformers.models.patchtsmixer', 'tra
nsformers.models.paligemma', 'transformers.models.owlvit', 'transformers.models.owlv2', 'transformers.models
.opt', 'transformers.models.openai', 'ftfy', 'ftfy.bad_codecs', 'transformers.models.oneformer', 'transforme
rs.models.olmo', 'transformers.models.nystromformer', 'transformers.models.nougat', 'transformers.models.nll
b_moe', 'transformers.models.nemotron', 'transformers.models.mvp', 'transformers.models.musicgen_melody', 't
ransformers.models.musicgen', 'transformers.models.mt5', 'transformers.models.mra', 'transformers.models.mpt
', 'transformers.models.mpnet', 'transformers.models.mobilevitv2', 'transformers.models.mobilevit', 'transfo
rmers.models.mobilenet_v2', 'transformers.models.mobilenet_v1', 'transformers.models.mobilebert', 'transform
ers.models.mixtral', 'transformers.models.mistral', 'transformers.models.mgp_str', 'transformers.models.mega
tron_bert', 'transformers.models.mbart', 'transformers.models.maskformer', 'transformers.models.mask2former'
, 'transformers.models.markuplm', 'transformers.models.marian', 'transformers.models.mamba2', 'transformers.
models.mamba', 'transformers.models.m2m_100', 'transformers.models.lxmert', 'transformers.models.luke', 'tra
nsformers.models.longt5', 'transformers.models.longformer', 'transformers.models.llava_next_video', 'transfo
rmers.models.llava_next', 'transformers.models.llava', 'transformers.models.llama', 'transformers.models.lil
t', 'transformers.models.levit', 'transformers.models.led', 'transformers.models.layoutxlm', 'transformers.m
odels.layoutlmv3', 'pytesseract', 'transformers.models.layoutlmv2', 'transformers.models.layoutlm', 'transfo
rmers.models.kosmos2', 'transformers.models.jetmoe', 'transformers.models.jamba', 'transformers.models.instr
uctblipvideo', 'transformers.models.instructblip', 'transformers.models.informer', 'transformers.models.imag
egpt', 'transformers.models.idefics2', 'transformers.models.idefics', 'transformers.models.ibert', 'transfor
mers.models.hubert', 'transformers.models.hiera', 'transformers.models.herbert', 'transformers.models.groupv
it', 'transformers.models.grounding_dino', 'transformers.models.gptj', 'transformers.models.gpt_neox_japanes
e', 'transformers.models.gpt_neox', 'transformers.models.gpt_neo', 'transformers.models.gpt_bigcode', 'trans
formers.models.gpt2', 'transformers.models.glpn', 'transformers.models.git', 'transformers.models.gemma2', '
transformers.models.gemma', 'transformers.models.fuyu', 'transformers.models.funnel', 'transformers.models.f
smt', 'transformers.models.focalnet', 'transformers.models.fnet', 'transformers.models.flava', 'transformers
.models.flaubert', 'transformers.models.fastspeech2_conformer', 'transformers.models.falcon', 'transformers.
models.esm', 'transformers.models.esm.openfold_utils', 'transformers.models.ernie', 'transformers.models.enc
oder_decoder', 'transformers.models.encodec', 'transformers.models.electra', 'transformers.models.efficientn
et', 'transformers.models.dpt', 'transformers.models.dpr', 'transformers.models.donut', 'transformers.models
.distilbert', 'transformers.models.dinov2', 'transformers.models.dinat', 'transformers.models.detr', 'transf
ormers.models.depth_anything', 'transformers.models.deprecated.xlm_prophetnet', 'transformers.models.depreca
ted.vit_hybrid', 'transformers.models.deprecated.van', 'transformers.models.deprecated.tvlt', 'transformers.
models.deprecated.transfo_xl', 'transformers.models.deprecated.trajectory_transformer', 'transformers.models
.deprecated.tapex', 'transformers.models.deprecated.speech_to_text_2', 'transformers.models.deprecated.retri
bert', 'transformers.models.deprecated.realm', 'transformers.models.deprecated.qdqbert', 'transformers.model
s.deprecated.open_llama', 'transformers.models.deprecated.nezha', 'transformers.models.deprecated.nat', 'tra
nsformers.models.deprecated.mmbt', 'transformers.models.deprecated.mega', 'transformers.models.deprecated.mc
tct', 'transformers.models.deprecated.jukebox', 'transformers.models.deprecated.graphormer', 'transformers.m
odels.deprecated.gptsan_japanese', 'transformers.models.deprecated.ernie_m', 'transformers.models.deprecated
.efficientformer', 'transformers.models.deprecated.deta', 'transformers.models.deit', 'transformers.models.d
eformable_detr', 'transformers.models.decision_transformer', 'transformers.models.deberta_v2', 'transformers
.models.deberta', 'transformers.models.dbrx', 'transformers.models.data2vec', 'transformers.models.cvt', 'tr
ansformers.models.ctrl', 'transformers.models.cpmant', 'transformers.models.convnextv2', 'transformers.model
s.convnext', 'transformers.models.convbert', 'transformers.models.conditional_detr', 'transformers.models.co
here', 'transformers.models.codegen', 'transformers.models.clvp', 'transformers.models.clipseg', 'transforme
rs.models.clip', 'transformers.models.clap', 'transformers.models.chinese_clip', 'transformers.models.chamel
eon', 'transformers.models.canine', 'transformers.models.camembert', 'transformers.models.byt5', 'transforme
rs.models.bros', 'transformers.models.bridgetower', 'transformers.models.bloom', 'transformers.models.blip_2
', 'transformers.models.blip', 'transformers.models.blenderbot_small', 'transformers.models.blenderbot', 'tr
ansformers.models.bit', 'transformers.models.biogpt', 'transformers.models.bigbird_pegasus', 'transformers.m
odels.big_bird', 'transformers.models.bertweet', 'emoji', 'emoji.unicode_codes', 'transformers.models.bert_j
apanese', 'transformers.models.bert_generation', 'transformers.models.bert', 'transformers.models.beit', 'tr
ansformers.models.bart', 'transformers.models.bark', 'transformers.models.autoformer', 'transformers.models.
auto', 'transformers.models.audio_spectrogram_transformer', 'transformers.models.altclip', 'transformers.mod
els.align', 'transformers.models.albert', 'transformers.integrations', 'transformers.generation', 'transform
ers.data', 'transformers.data.metrics', 'sklearn.metrics', 'sklearn.metrics._pairwise_distances_reduction', 
'sklearn.utils', 'sklearn.externals._packaging', 'sklearn.externals', 'sklearn.preprocessing', 'sklearn.mode
l_selection', 'sklearn.cluster', 'sklearn.neighbors', 'sklearn.tree', 'sklearn.linear_model', 'sklearn.svm',
 'sklearn._loss', 'sklearn.linear_model._glm', 'sklearn.covariance', 'sklearn.decomposition', 'sklearn.manif
old', 'sklearn.cluster._hdbscan', 'scipy.cluster', 'sklearn.gaussian_process', 'sklearn.metrics._plot', 'skl
earn.metrics.cluster', 'sklearn', 'sklearn.__check_build', 'transformers.agents', 'transformers.utils', 'tra
nsformers.pipelines', 'torch.testing._internal.codegen', 'torch.testing._internal', 'torch.quantization.fx',
 'torch.package.analyze', 'torch.package', 'functorch.compile', 'torch.onnx._internal.fx.passes', 'torch.onn
x._internal.fx', 'torch.onnx._internal.fx.analysis', 'torch.onnx._internal.diagnostics.infra.sarif', 'torch.
onnx._internal.diagnostics.infra', 'torch.onnx._internal.diagnostics', 'torch.onnx._internal', 'torch.nn.uti
ls._expanded_weights', 'torch.nn.utils', 'torch.nn.quantized.modules', 'torch.nn.quantized.dynamic.modules',
 'torch.nn.quantized.dynamic', 'torch.nn.quantized._reference.modules', 'torch.nn.quantized._reference', 'to
rch.nn.quantizable.modules', 'torch.nn.qat.modules', 'torch.nn.qat.dynamic.modules', 'torch.nn.qat.dynamic',
 'torch.nn.parallel', 'torch.nn.modules', 'torch.nn.intrinsic.quantized.modules', 'torch.nn.intrinsic.quanti
zed.dynamic.modules', 'torch.nn.intrinsic.quantized.dynamic', 'torch.nn.intrinsic.quantized', 'torch.nn.intr
insic.qat.modules', 'torch.nn.intrinsic.qat', 'torch.nn.intrinsic.modules', 'torch.nn.backends', 'torch.moni
tor', 'torch.masked.maskedtensor', 'torch.jit.mobile', 'torch.jit._passes', 'torch.fx.passes.utils', 'torch.
fx.passes.tests', 'torch.fx.passes.infra', 'torch.fx.passes.dialect.common', 'torch.fx.passes.dialect', 'tor
ch.fx.passes.backends', 'torch.fx.passes', 'torch.fx.experimental.unification.multipledispatch', 'torch.fx.e
xperimental.unification', 'torch.fx.experimental.migrate_gradual_types', 'torch.fx.experimental', 'torch.fx'
, 'torch.distributed.tensor.parallel', 'torch.distributed.tensor', 'torch.distributed.rpc._testing', 'torch.
distributed.rpc', 'torch.distributed.pipeline', 'torch.distributed.optim', 'torch.distributed.nn.jit.templat
es', 'torch.distributed.nn.jit', 'torch.distributed.nn.api', 'torch.distributed.nn', 'torch.distributed.laun
cher', 'torch.distributed.fsdp', 'torch.distributed.elastic.utils.data', 'torch.distributed.elastic.utils', 
'torch.distributed.elastic.timer', 'torch.distributed.elastic.rendezvous', 'torch.distributed.elastic.multip
rocessing.errors', 'torch.distributed.elastic.multiprocessing', 'torch.distributed.elastic.metrics', 'torch.
distributed.elastic.events', 'torch.distributed.elastic.agent.server', 'torch.distributed.elastic.agent', 't
orch.distributed.elastic', 'torch.distributed.checkpoint', 'torch.distributed.autograd', 'torch.distributed.
algorithms.model_averaging', 'torch.distributed.algorithms.ddp_comm_hooks', 'torch.distributed.algorithms._q
uantization', 'torch.distributed.algorithms._optimizer_overlap', 'torch.distributed.algorithms._comm_hooks',
 'torch.distributed.algorithms._checkpoint', 'torch.distributed.algorithms', 'torch.distributed._tools', 'to
rch.distributed._tensor.ops', 'torch.distributed._tensor.debug', 'torch.distributed._tensor', 'torch.distrib
uted._spmd', 'torch.distributed._sharding_spec', 'torch.distributed._sharded_tensor', 'torch.distributed._sh
ard.sharding_spec.chunk_sharding_spec_ops', 'torch.distributed._shard.sharding_spec', 'torch.distributed._sh
ard.sharding_plan', 'torch.distributed._shard.sharded_tensor._ops', 'torch.distributed._shard.sharded_tensor
', 'torch.distributed._shard.sharded_optim', 'torch.distributed._shard.checkpoint', 'torch.distributed._shar
d', 'torch.distributed._composable', 'torch.distributed', 'torch.cuda.amp', 'torch.cpu.amp', 'torch.contrib'
, 'torch.backends.xnnpack', 'torch.backends.xeon', 'torch.backends.quantized', 'torch.backends.opt_einsum', 
'torch.backends.openmp', 'torch.backends.mps', 'torch.backends.mkldnn', 'torch.backends.mkl', 'torch.backend
s.cudnn', 'torch.backends.cuda', 'torch.backends.cpu', 'torch.backends._nnapi', 'torch.backends._coreml', 't
orch.autograd._functions', 'torch.ao.quantization.quantizer', 'torch.ao.quantization.pt2e.representation', '
torch.ao.quantization.pt2e', 'torch.ao.quantization.fx._model_report', 'torch.ao.quantization.fx', 'torch.ao
.quantization.backend_config', 'torch.ao.quantization', 'torch.ao.pruning.sparsifier', 'torch.ao.pruning.sch
eduler', 'torch.ao.pruning._experimental.pruner', 'torch.ao.pruning._experimental.data_sparsifier.lightning.
callbacks', 'torch.ao.pruning._experimental.data_sparsifier.lightning', 'torch.ao.pruning._experimental.data
_sparsifier', 'torch.ao.pruning._experimental.data_scheduler', 'torch.ao.pruning._experimental.activation_sp
arsifier', 'torch.ao.pruning._experimental', 'torch.ao.pruning', 'torch.ao.ns.fx', 'torch.ao.ns', 'torch.ao.
nn.sparse.quantized.dynamic', 'torch.ao.nn.sparse.quantized', 'torch.ao.nn.sparse', 'torch.ao.nn.quantized.r
eference.modules', 'torch.ao.nn.quantized.reference', 'torch.ao.nn.quantized.modules', 'torch.ao.nn.quantize
d.dynamic.modules', 'torch.ao.nn.quantized.dynamic', 'torch.ao.nn.quantized', 'torch.ao.nn.quantizable.modul
es', 'torch.ao.nn.quantizable', 'torch.ao.nn.qat.modules', 'torch.ao.nn.qat.dynamic.modules', 'torch.ao.nn.q
at.dynamic', 'torch.ao.nn.qat', 'torch.ao.nn.intrinsic.quantized.modules', 'torch.ao.nn.intrinsic.quantized.
dynamic.modules', 'torch.ao.nn.intrinsic.quantized.dynamic', 'torch.ao.nn.intrinsic.quantized', 'torch.ao.nn
.intrinsic.qat.modules', 'torch.ao.nn.intrinsic.qat', 'torch.ao.nn.intrinsic.modules', 'torch.ao.nn.intrinsi
c', 'torch.ao.nn', 'torch._subclasses', 'torch._refs.special', 'torch._refs.nn.functional', 'torch._refs.nn'
, 'torch._refs.linalg', 'torch._prims_common', 'torch._prims', 'torch._numpy.testing', 'torch._numpy', 'torc
h._lazy', 'torch._inductor.kernel', 'torch._inductor.fx_passes', 'torch._inductor.codegen', 'torch._higher_o
rder_ops', 'torch._functorch', 'torch._export.serde', 'torch._export.passes', 'torch._export.pass_infra', 't
orch._export.db.examples', 'torch._export.db', 'torch._export', 'torch._dynamo.variables', 'torch._dynamo.re
pro', 'einops.layers', 'einops', 'torch._dynamo.backends', 'torch._dispatch', 'torch._decomp', 'torchgen.api
.types', 'torchgen.api', 'torch._custom_op', 'win32com.shell', 'win32com', 'win32com.client', 'pywin.mfc', '
pywin.dialogs', 'pywin', 'win32com.server', 'torch._logging', 'torch.onnx', 'torch._dynamo', 'torch.compiler
', 'torch.func', 'torch.export', 'torch._inductor', 'torch.masked', 'torch.quantization', 'torch.nn.intrinsi
c', 'torch.nn.qat', 'torch.nn.quantized', 'torch.nn.quantizable', 'torch.ao', 'torch.profiler', 'torch.utils
.data', 'torch.backends', 'torch.testing', 'torch.distributions', 'torch.linalg', 'torch.jit', 'torch.utils.
backcompat', 'torch.special', 'torch.sparse', 'torch.multiprocessing', 'torch.optim._multi_tensor', 'torch.o
ptim', 'torch.signal.windows', 'torch.signal', 'torch.nn', 'torch.nested', 'torch._awaits', 'torch.futures',
 'torch.fft', 'torch.autograd', 'torch.mps', 'torch.cpu', 'torch.cuda', 'torch.amp', 'torch._refs', 'scipy._
lib.array_api_compat.cupy', 'scipy._lib.array_api_compat.dask.array', 'scipy._lib.array_api_compat.dask', 's
cipy._lib.array_api_compat.common', 'scipy.integrate', 'scipy.integrate._rules', 'scipy.integrate._ivp', 'sc
ipy.stats', 'scipy.stats._rcont', 'scipy.stats._levy_stable', 'scipy._lib', 'networkx.linalg', 'networkx.alg
orithms', 'networkx.algorithms.tree', 'networkx.algorithms.isomorphism', 'networkx.algorithms.flow', 'networ
kx.algorithms.community', 'networkx.algorithms.connectivity', 'networkx.algorithms.bipartite', 'networkx.alg
orithms.approximation', 'networkx.algorithms.traversal', 'networkx.algorithms.shortest_paths', 'networkx.alg
orithms.operators', 'networkx.algorithms.minors', 'networkx.algorithms.link_analysis', 'networkx.algorithms.
coloring', 'networkx.algorithms.components', 'networkx.algorithms.centrality', 'networkx.algorithms.assortat
ivity', 'networkx.readwrite', 'networkx.readwrite.json_graph', 'networkx.generators', 'networkx.classes', 'n
etworkx.utils', 'nltk.tree', 'nltk.translate', 'nltk.tokenize', 'nltk.tag', 'nltk.parse', 'sklearn.datasets'
, 'sklearn.feature_extraction', 'nltk.metrics', 'nltk.inference', 'nltk.classify', 'nltk.chunk', 'nltk.lm', 
'nltk.corpus', '_pyi_rth_utils', 'collections', 'encodings', 'src.ui', 'src', 'fitz', 'fontTools.subset', 'f
ontTools.misc', 'fontTools.misc.plistlib', 'fontTools.encodings', 'fontTools.pens', 'fontTools.colorLib', 'f
ontTools.unicodedata', 'fontTools.designspaceLib', 'fontTools.varLib', 'fontTools.cffLib', 'fontTools.otlLib
.optimize', 'fontTools.otlLib', 'fontTools.feaLib', 'fontTools.ttLib.tables', 'lz4.block', 'fontTools.ttLib'
, 'fontTools.config', 'fontTools', 'src.core', 'src.utils', 'pdf2image', 'unstructured.partition', 'unstruct
ured', 'pypandoc', 'unstructured.nlp', 'langdetect', 'langdetect.utils', 'iso639', 'iso639._data', 'unstruct
ured_inference', 'unstructured_inference.inference', 'unstructured_inference.models', 'layoutparser.elements
', 'layoutparser', 'pdfplumber', 'pypdfium2', 'pypdfium2.internal', 'pypdfium2_raw', 'pypdfium2._helpers', '
pdfplumber.utils', 'unstructured_inference.patches', 'onnxruntime.quantization', 'onnxruntime.tools', 'onnxr
untime.quantization.operators', 'onnxruntime.quantization.CalTableFlatBuffers', 'flatbuffers', 'layoutparser
.models', 'layoutparser.models.effdet', 'effdet.data', 'effdet.data.parsers', 'effdet.object_detection', 'ef
fdet', 'effdet.config', 'omegaconf', 'omegaconf.resolvers.oc', 'omegaconf.grammar.gen', 'omegaconf.grammar',
 'antlr4.error', 'antlr4.tree', 'antlr4.atn', 'antlr4.dfa', 'antlr4', 'antlr4.xpath', 'omegaconf.resolvers',
 'effdet.evaluation', 'timm.models.layers', 'iopath.common', 'iopath', 'iopath.tabular', 'portalocker', 'lay
outparser.models.paddledetection', 'layoutparser.models.detectron2', 'pdfminer', 'unstructured.partition.uti
ls', 'pptx', 'pptx.oxml.dml', 'pptx.enum', 'pptx.oxml.shapes', 'pptx.oxml', 'pptx.oxml.chart', 'pptx.dml', '
pptx.chart', 'pptx.parts', 'pptx.opc', 'pptx.text', 'pptx.shapes', 'unstructured.documents', 'unstructured.f
ile_utils', 'filetype', 'filetype.types', 'magic', 'unstructured.cleaners', 'unstructured.chunking', 'docx.t
ext', 'docx.oxml', 'docx.oxml.text', 'docx.enum', 'docx.dml', 'docx.styles', 'docx.opc', 'docx.opc.parts', '
docx.parts', 'docx.image', 'docx.drawing', 'unstructured.partition.utils.ocr_models', 'unstructured_pytesser
act', 'docx', 'jieba.analyse', 'whoosh.lang', 'whoosh.lang.snowball', 'jieba.posseg', 'whoosh.util', 'whoosh
.filedb', 'whoosh.analysis', 'whoosh.support', 'whoosh.query', 'whoosh.matching', 'whoosh.automata', 'whoosh
.codec', 'whoosh.qparser', 'whoosh', 'optimum.onnxruntime', 'optimum.onnx', 'onnxruntime.transformers', 'opt
imum.exporters.onnx', 'optimum.commands.export', 'optimum.commands', 'optimum.exporters.tflite', 'optimum.ut
ils.preprocessing', 'optimum.exporters', 'optimum.onnxruntime.preprocessors', 'coloredlogs', 'humanfriendly.
terminal', 'humanfriendly', 'onnx.tools', 'sentence_transformers.losses', 'sentence_transformers.cross_encod
er.losses', 'sentence_transformers.readers', 'sentence_transformers.datasets', 'sentence_transformers.cross_
encoder', 'sentence_transformers.models', 'sentence_transformers.models.tokenizer', 'sentence_transformers.e
valuation', 'sentence_transformers', 'overrides', 'chromadb', 'chromadb.utils', 'tenacity', 'tenacity.asynci
o', 'chromadb.api', 'chromadb.auth.token', 'chromadb.telemetry.opentelemetry', 'chromadb.telemetry', 'opente
lemetry.exporter.otlp.proto.grpc.trace_exporter', 'opentelemetry.exporter.otlp.proto.grpc', 'opentelemetry.s
dk.environment_variables', 'opentelemetry.sdk.util', 'deprecated', 'opentelemetry.proto.trace.v1', 'opentele
metry.proto.trace', 'opentelemetry.proto', 'opentelemetry.proto.resource.v1', 'opentelemetry.proto.resource'
, 'opentelemetry.proto.common.v1', 'opentelemetry.proto.common', 'opentelemetry.proto.collector.trace.v1', '
opentelemetry.proto.collector.trace', 'opentelemetry.proto.collector', 'opentelemetry.sdk.metrics.export', '
opentelemetry.sdk.metrics', 'opentelemetry.attributes', 'opentelemetry.metrics._internal', 'opentelemetry.en
vironment_variables', 'opentelemetry.metrics', 'opentelemetry.sdk.metrics._internal', 'opentelemetry.sdk.met
rics._internal.exponential_histogram.mapping', 'opentelemetry.sdk.metrics._internal.export', 'opentelemetry.
context', 'opentelemetry.exporter.otlp.proto.common._internal', 'opentelemetry.exporter.otlp.proto.common', 
'opentelemetry.exporter.otlp.proto.common._internal.trace_encoder', 'opentelemetry.sdk.trace.export', 'opent
elemetry.sdk.trace', 'opentelemetry.semconv', 'opentelemetry.sdk.resources', 'opentelemetry.semconv.resource
', 'opentelemetry.trace', 'opentelemetry.trace.propagation', 'opentelemetry.exporter.otlp.proto.grpc._log_ex
porter', 'opentelemetry.exporter.otlp.proto.common._internal._log_encoder', 'opentelemetry.sdk._logs', 'open
telemetry.sdk._logs._internal', 'opentelemetry.semconv.trace', 'opentelemetry._logs', 'opentelemetry._logs.s
everity', 'opentelemetry._logs._internal', 'opentelemetry.baggage.propagation', 'opentelemetry.baggage', 'op
entelemetry.sdk._logs.export', 'opentelemetry.sdk._logs._internal.export', 'opentelemetry.exporter.otlp.prot
o.grpc.metric_exporter', 'opentelemetry.exporter.otlp.proto.common._internal.metrics_encoder', 'opentelemetr
y.sdk.metrics.view', 'opentelemetry.proto.metrics.v1', 'opentelemetry.proto.metrics', 'opentelemetry.proto.c
ollector.metrics.v1', 'opentelemetry.proto.collector.metrics', 'chromadb.auth', 'chromadb.telemetry.product'
, 'loguru', 'win32_setctime', 'multiprocessing']
PS D:\LocalQA>
```

---

已完成以下关键步骤：

1. **修正了 resource_path 路径处理函数**，确保打包后所有模型、配置、静态资源等都能被正确访问。
2. **新建并配置了 localqa.spec**，将 models、config.yaml、data、static 等资源全部打包进可执行文件目录。
3. **已开始执行 PyInstaller 打包命令**，正在生成 dist/LocalQA 可执行包。

**下一步请等待打包完成**，然后你可以在 `dist/LocalQA` 目录下找到可执行文件，直接运行测试所有功能（包括模型加载和推理）。

如遇到任何报错或模型依然无法加载，请将报错信息发给我，我会继续协助你修复，直到“开箱即用”。  
如果需要自动化测试脚本或交付文档，也可以随时告知！

---

_**User**_

目前打包是不是完成了

---

_**Assistant**_




---

<details>
            <summary>Listed directory dist • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Listed directory build • **1** results</summary>
        
| Name |
|-------|
| 📁 `localqa` |

</details>

---

```bash
pyinstaller localqa.spec
```

```
PS D:\LocalQA> pyinstaller localqa.spec
154 INFO: PyInstaller: 6.14.1, contrib hooks: 2025.5
155 INFO: Python: 3.10.11
159 INFO: Platform: Windows-10-10.0.22631-SP0
159 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python310
160 INFO: Module search paths (PYTHONPATH):
['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Scripts\\pyinstaller.exe',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.zip',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor',  
 'D:\\LocalQA']
196 WARNING: discover_hook_directories: Failed to process hook entry point 'EntryPoint(name='hook-dirs', val
ue='rapidfuzz.__pyinstaller:get_hook_dirs', group='pyinstaller40')': AttributeError: module 'rapidfuzz.__pyi
nstaller' has no attribute 'get_hook_dirs'
468 INFO: Appending 'datas' from .spec
532 INFO: checking Analysis
532 INFO: Building Analysis because Analysis-00.toc is non existent
532 INFO: Running Analysis Analysis-00.toc
532 INFO: Target bytecode optimization level: 0
532 INFO: Initializing module dependency graph...
533 INFO: Initializing module graph hook caches...
541 INFO: Analyzing modules for base_library.zip ...
1049 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
1075 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
1711 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2152 INFO: Caching module dependency graph...
2174 INFO: Looking for Python shared library...
2176 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python310.dll
2176 INFO: Analyzing D:\LocalQA\main.py
2193 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2248 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2583 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2592 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'      
2593 INFO: SetuptoolsInfo: initializing cached setuptools info...
966 WARNING: Failed to collect submodules for 'setuptools._vendor.packaging.licenses' because importing 'set
uptools._vendor.packaging.licenses' raised: ModuleNotFoundError: No module named 'packaging.licenses'       
4466 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
4489 INFO: Processing standard module hook 'hook-torch.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
1127 WARNING: Failed to collect submodules for 'torch.utils.tensorboard' because importing 'torch.utils.tens
orboard' raised: ModuleNotFoundError: No module named 'tensorboard'
[2025-07-01 12:20:18,157] torch.distributed.elastic.multiprocessing.redirects: [WARNING] NOTE: Redirects are
 currently not supported in Windows or MacOs.
9328 INFO: hook-torch: this torch build does not depend on MKL...
9329 INFO: hook-torch: raising recursion limit to 5000
9340 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9375 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9658 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9658 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptool
s._vendor.jaraco.text'!
9661 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9665 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9666 INFO: Processing pre-find-module-path hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
9827 INFO: Processing standard module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9835 INFO: Processing standard module hook 'hook-distutils.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9848 INFO: Processing standard module hook 'hook-_osx_support.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9868 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9912 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'       
9912 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setu
ptools._vendor.jaraco.functools'!
9916 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'     
9936 INFO: Processing standard module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9937 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10038 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10284 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10347 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\
\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10348 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\App
Data\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'   
10349 INFO: Processing standard module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10360 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'        
10361 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setup
tools._vendor.jaraco.context'!
10365 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'     
10365 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'se
tuptools._vendor.backports.tarfile'!
10391 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10392 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10396 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10502 INFO: Processing standard module hook 'hook-numpy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10706 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
11124 INFO: Processing standard module hook 'hook-psutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
11877 INFO: Processing standard module hook 'hook-sympy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
12262 INFO: Processing standard module hook 'hook-matplotlib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12475 INFO: Processing pre-safe-import-module hook 'hook-gi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
12569 INFO: Processing standard module hook 'hook-matplotlib.backend_bases.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12605 INFO: Processing standard module hook 'hook-PIL.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12671 INFO: Processing standard module hook 'hook-PIL.Image.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12929 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13027 INFO: Processing standard module hook 'hook-PIL.ImageFilter.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13278 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13470 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13497 INFO: Processing standard module hook 'hook-matplotlib.pyplot.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13985 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14014 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
14509 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14590 INFO: Processing standard module hook 'hook-py.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14865 INFO: Processing standard module hook 'hook-pytest.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15118 INFO: Processing standard module hook 'hook-pygments.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
19296 INFO: Processing standard module hook 'hook-scipy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
20572 INFO: Processing standard module hook 'hook-lxml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
20823 INFO: Processing standard module hook 'hook-lxml.etree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
20919 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
21337 INFO: Processing standard module hook 'hook-scipy.linalg.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
21605 INFO: Processing standard module hook 'hook-scipy.sparse.csgraph.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
21632 INFO: Processing standard module hook 'hook-scipy.special._ufuncs.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
21673 INFO: Processing standard module hook 'hook-scipy.special._ellip_harm_2.py' from 'C:\\Users\\<USER>\\Ap
pData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
22120 INFO: Processing standard module hook 'hook-scipy.spatial.transform.rotation.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
22670 INFO: Processing standard module hook 'hook-scipy.stats._stats.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
24439 INFO: Processing standard module hook 'hook-xml.dom.domreg.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
25393 INFO: Processing standard module hook 'hook-pandas.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
26293 INFO: Processing standard module hook 'hook-pytz.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
26942 INFO: Processing standard module hook 'hook-pandas.io.formats.style.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
27698 INFO: Processing standard module hook 'hook-pyarrow.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28168 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28530 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28565 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29234 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
29320 INFO: Processing standard module hook 'hook-bcrypt.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29360 INFO: Processing standard module hook 'hook-fsspec.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29707 INFO: Processing standard module hook 'hook-lz4.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
30189 INFO: Processing standard module hook 'hook-pandas.plotting.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
30573 INFO: Processing standard module hook 'hook-openpyxl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
31439 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
33273 INFO: Processing standard module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
33512 INFO: Processing standard module hook 'hook-pandas.io.clipboard.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
35435 INFO: Processing standard module hook 'hook-shelve.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
36283 INFO: Processing standard module hook 'hook-transformers.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
37786 INFO: Processing standard module hook 'hook-pydantic.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\experimental\__init__.py:7
: PydanticExperimentalWarning: This module is experimental, its contents are subject to change and deprecati
on.
  warnings.warn(
38743 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
39746 INFO: Processing standard module hook 'hook-anyio.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40248 INFO: Processing standard module hook 'hook-orjson.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
41062 INFO: Processing standard module hook 'hook-websockets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
41937 INFO: Processing standard module hook 'hook-uvicorn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
42629 INFO: Processing standard module hook 'hook-bitsandbytes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
42722 INFO: Processing standard module hook 'hook-torchvision.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
42753 INFO: Processing standard module hook 'hook-torchvision.io.image.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
42972 INFO: Processing standard module hook 'hook-torchaudio.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
46277 INFO: Processing standard module hook 'hook-scipy.io.matlab.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
47283 INFO: Processing standard module hook 'hook-onnxruntime.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
47641 INFO: Processing pre-safe-import-module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'      
47705 INFO: Processing standard module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
47706 INFO: Processing standard module hook 'hook-pythoncom.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
47770 INFO: Processing standard module hook 'hook-pywintypes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
48543 INFO: Processing standard module hook 'hook-wcwidth.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
49133 INFO: Processing standard module hook 'hook-datasets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
49205 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
49205 INFO: TclTkInfo: initializing cached Tcl/Tk info...
49327 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
49647 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
50178 INFO: Processing standard module hook 'hook-regex.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
50224 INFO: Processing standard module hook 'hook-spacy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
54080 INFO: Processing standard module hook 'hook-thinc.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
55665 INFO: Processing standard module hook 'hook-srsly.msgpack._packer.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
56030 INFO: Processing pre-safe-import-module hook 'hook-tensorflow.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'    
56064 INFO: Processing standard module hook 'hook-thinc.backends.numpy_ops.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
56233 INFO: Processing standard module hook 'hook-langcodes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
58120 INFO: Processing standard module hook 'hook-jieba.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
58529 INFO: Processing standard module hook 'hook-astor.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
61889 INFO: Processing standard module hook 'hook-cv2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
63008 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
63024 INFO: Processing standard module hook 'hook-grpc.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
64740 INFO: Processing standard module hook 'hook-timm.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
67333 INFO: Processing standard module hook 'hook-emoji.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
72231 INFO: Processing standard module hook 'hook-nltk.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
73439 INFO: Processing standard module hook 'hook-sklearn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
73717 INFO: Processing standard module hook 'hook-sklearn.utils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
73769 INFO: Processing standard module hook 'hook-sklearn.metrics.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
74886 INFO: Processing standard module hook 'hook-sklearn.metrics.cluster.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75241 INFO: Processing standard module hook 'hook-sklearn.cluster.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75262 INFO: Processing standard module hook 'hook-sklearn.metrics.pairwise.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75340 INFO: Processing standard module hook 'hook-sklearn.neighbors.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75375 INFO: Processing standard module hook 'hook-sklearn.linear_model.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
84683 INFO: Processing standard module hook 'hook-PyQt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
85209 INFO: Processing standard module hook 'hook-PyQt6.QtWidgets.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
85681 INFO: Processing standard module hook 'hook-PyQt6.QtCore.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
85943 INFO: Processing standard module hook 'hook-PyQt6.QtGui.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
87203 INFO: Processing standard module hook 'hook-opentelemetry.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90045 INFO: Processing standard module hook 'hook-docx.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90227 INFO: Processing standard module hook 'hook-pdfminer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90422 INFO: Processing standard module hook 'hook-pptx.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91643 INFO: Processing standard module hook 'hook-pypdfium2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91655 INFO: Processing standard module hook 'hook-pypdfium2_raw.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91841 INFO: Processing standard module hook 'hook-magic.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91874 INFO: Processing standard module hook 'hook-iso639.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91883 INFO: Processing standard module hook 'hook-langdetect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\whoosh\codec\whoosh3.py:1116: Synta
xWarning: "is" with a literal. Did you mean "=="?
  elif fixedsize is 0:
94435 INFO: Processing standard module hook 'hook-PyQt6.QtWebEngineWidgets.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94545 INFO: Processing standard module hook 'hook-PyQt6.QtNetwork.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94708 INFO: Processing standard module hook 'hook-PyQt6.QtPrintSupport.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94773 INFO: Processing standard module hook 'hook-PyQt6.QtWebChannel.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94852 INFO: Processing standard module hook 'hook-PyQt6.QtWebEngineCore.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94966 INFO: Processing module hooks (post-graph stage)...
95252 INFO: Processing standard module hook 'hook-lxml.isoschematron.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
95395 INFO: Processing standard module hook 'hook-zope.interface.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
95433 INFO: Processing standard module hook 'hook-sklearn.tree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
99573 WARNING: Hidden import "importlib_resources.trees" not found!
99574 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
99574 INFO: Matplotlib backend selection method: automatic discovery of used backends
99580 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'sympy.testing.runtests':
 ['Agg', 'Agg']
99582 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'gradio.utils': ['agg']  
99582 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'paddle.dataset.uci_housi
ng': ['Agg']
99584 INFO: The following Matplotlib backends were discovered by scanning for `matplotlib.use()` calls: ['Ag
g', 'agg']. If your backend of choice is not in this list, either add a `matplotlib.use()` call to your code
, or configure the backend collection via hook options (see: https://pyinstaller.org/en/stable/hooks-config.
html#matplotlib-hooks).
99584 INFO: Selected matplotlib backends: ['Agg', 'agg']
99866 INFO: Processing standard module hook 'hook-PIL.SpiderImagePlugin.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
100645 INFO: Processing standard module hook 'hook-PyQt6.QtPositioning.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
100864 INFO: Processing standard module hook 'hook-PyQt6.QtQuick.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
100958 INFO: Processing standard module hook 'hook-PyQt6.QtQml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101369 INFO: Processing standard module hook 'hook-PyQt6.QtOpenGL.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101470 INFO: Processing standard module hook 'hook-PyQt6.QtQuickWidgets.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101548 WARNING: Hidden import "scipy.special._cdflib" not found!
101806 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
102023 WARNING: Hidden import "pysqlite2" not found!
102024 WARNING: Hidden import "MySQLdb" not found!
102024 WARNING: Hidden import "psycopg2" not found!
102387 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
102388 INFO: Processing standard module hook 'hook-lxml.objectify.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
102389 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
102832 INFO: Performing binary vs. data reclassification (7919 entries)
103209 INFO: Looking for ctypes DLLs
103263 WARNING: Ignoring /usr/lib64/libgomp.so.1 imported from C:\Users\<USER>\AppData\Local\Programs\Python\
Python310\lib\site-packages\torch\_inductor\codecache.py - only basenames are supported with ctypes imports!
103324 INFO: Analyzing run-time hooks ...
103359 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103361 INFO: Including run-time hook 'pyi_rth_pyqt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Py
thon\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103362 INFO: Processing pre-find-module-path hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
103363 INFO: Processing standard module hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
103364 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103365 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103366 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103367 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103368 INFO: Including run-time hook 'pyi_rth_mplconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103372 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103372 INFO: Including run-time hook 'pyi_rth_nltk.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Pyt
hon\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103373 INFO: Including run-time hook 'pyi_rth_pywintypes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103373 INFO: Including run-time hook 'pyi_rth_pythoncom.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103373 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103586 INFO: Creating base_library.zip...
103632 INFO: Looking for dynamic libraries
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py:
227: UserWarning: The numpy.array_api submodule is still experimental. See NEP 47.
  __import__(package)
Could not find an up-to-date installation of `packaging`. License expressions might not be validated. To enf
orce validation, please install `packaging>=24.2`.
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py:
227: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resour
ces.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this pa
ckage or pin to Setuptools<81.
  __import__(package)
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\experimental\__init__.py:7
: PydanticExperimentalWarning: This module is experimental, its contents are subject to change and deprecati
on.
  warnings.warn(
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 287, in find_binary_dependencies
    child.call(import_library, package)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\isolated\_paren
t.py", line 322, in call
    raise SubprocessDiedError(
PyInstaller.isolated._parent.SubprocessDiedError: Child process died calling import_library() with args=('on
nx.reference',) and kwargs={}. Its exit code was 3221225477.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_ma
in
    return _run_code(code, main_globals, None,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\pyinstaller.exe\__main__.py", line 7,
 in <module>
    sys.exit(_console_script_run())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", l
ine 231, in _console_script_run
    run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", l
ine 215, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", l
ine 70, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 1282, in main
    build(specfile, distpath, workpath, clean_build)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 1220, in build
    exec(code, spec_namespace)
  File "localqa.spec", line 5, in <module>
    a = Analysis(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 584, in __init__
    self.__postinit__()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\datast
ruct.py", line 184, in __postinit__
    self.assemble()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 977, in assemble
    find_binary_dependencies(self.binaries, collected_packages, self.graph._bindepend_symlink_suppression)  
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_
main.py", line 291, in find_binary_dependencies
    raise isolated.SubprocessDiedError(
PyInstaller.isolated._parent.SubprocessDiedError: Isolated subprocess crashed while importing package 'onnx.
reference'! Package import list: ['PyQt6', 'importlib', 'logging', 'importlib.metadata', 'urllib', 'email', 
'xmlrpc', 'xml.parsers', 'xml', 'http', 'xml.sax', 'ctypes', 'multiprocessing.dummy', 'nltk', 'xml.etree', '
nltk.tbl', 'nltk.misc', 'json', 'yaml', 'nltk.app', 'numpy.ma', 'numpy.testing', 'numpy.distutils', 'pydoc_d
ata', 'pyreadline3.console', 'ctypes.macholib', 'pyreadline3.logger', 'pyreadline3.keysyms', 'pyreadline3', 
'pyreadline3.modes', 'pyreadline3.lineeditor', 'pyreadline3.clipboard', 'numpy.core', 'numpy.compat', 'numpy
.lib', 'numpy.matrixlib', 'psutil', 'numpy.testing._private', 'unittest', 'asyncio', 'concurrent.futures', '
concurrent', 'numpy.array_api', 'numpy.typing', 'numpy._typing', 'numpy.linalg', 'numpy.random', 'numpy.fft'
, 'pyparsing', 'html', 'pyparsing.diagram', 'jinja2', 'markupsafe', 'packaging', 'contourpy', 'dateutil.tz',
 'dateutil.zoneinfo', 'dateutil.parser', 'dateutil', 'olefile', 'PIL', 'matplotlib.projections', 'mpl_toolki
ts.mplot3d', 'matplotlib.tri', 'matplotlib.backends', 'matplotlib.axes', 'kiwisolver', 'matplotlib.style', '
matplotlib._api', 'cycler', 'matplotlib', 'certifi', 'nltk.draw', 'regex', 'nltk.corpus.reader', 'sqlite3', 
'nltk.ccg', 'tkinter', 'nltk.cluster', 'numpy', 'numpy.polynomial', 'nltk.stem', 'nltk.sem', 'networkx', 'ne
tworkx.drawing', 'scipy', 'scipy._lib.array_api_compat.numpy', 'scipy._lib.array_api_compat', 'scipy._lib.ar
ray_api_compat.torch', 'torch', 'torch.utils.viz', 'torch.utils.model_dump', 'torch.utils.jit', 'torch.utils
.hipify', 'tabulate', 'wcwidth', 'dill', 'dbm', 'scipy.io', 'scipy.io.arff', 'scipy.io._harwell_boeing', 'sc
ipy.sparse', 'scipy.sparse.csgraph', 'scipy.sparse.linalg', 'scipy.linalg', 'scipy.special', 'scipy._lib.arr
ay_api_extra', 'scipy.fft', 'scipy.fft._pocketfft', 'scipy._lib._uarray', 'scipy.sparse.linalg._eigen', 'sci
py.sparse.linalg._eigen.lobpcg', 'scipy.sparse.linalg._eigen.arpack', 'scipy.sparse.linalg._dsolve', 'scipy.
sparse.linalg._isolve', 'scipy.io._fast_matrix_market', 'scipy.io.matlab', 'torchaudio', 'torchaudio.backend
', 'torchaudio._internal', 'torchaudio._backend', 'torchaudio.utils', 'torchaudio.transforms', 'torchaudio.s
ox_effects', 'torchaudio.pipelines', 'sentencepiece', 'torchaudio.pipelines._wav2vec2', 'torchaudio.pipeline
s._tts', 'torchaudio.models', 'torchaudio.models.wav2vec2', 'torchaudio.models.wav2vec2.utils', 'torchaudio.
models.squim', 'torchaudio.io', 'torchaudio.functional', 'torchaudio.datasets', 'torchaudio.compliance', 'to
rchaudio._extension', 'torchvision.io', 'torchvision', 'torchvision.ops', 'torchvision.models', 'torchvision
.models.video', 'torchvision.models.segmentation', 'torchvision.models.quantization', 'torchvision.models.op
tical_flow', 'torchvision.models.detection', 'torchvision.datasets', 'pycocotools', 'torchvision.transforms.
v2.functional', 'torchvision.transforms.v2', 'torchvision.tv_tensors', 'requests', 'idna', 'urllib3.util', '
zstandard', 'urllib3.http2', 'cryptography', 'cryptography.hazmat.bindings', 'cryptography.hazmat', 'cryptog
raphy.hazmat.bindings.openssl', 'cryptography.hazmat.decrepit.ciphers', 'cryptography.hazmat.decrepit', 'cry
ptography.hazmat.primitives.ciphers', 'cryptography.hazmat.primitives.asymmetric', 'cryptography.hazmat.prim
itives', 'cryptography.hazmat.primitives.serialization', 'bcrypt', 'cryptography.hazmat.backends.openssl', '
cryptography.hazmat.backends', 'cryptography.x509', 'urllib3.contrib', 'chardet', 'charset_normalizer', 'url
lib3', 'urllib3.contrib.emscripten', 'lmdb', 'cffi', 'distutils.command', 'distutils', 'setuptools._distutil
s', 'setuptools._distutils.compat', 'setuptools._vendor.jaraco.functools', 'more_itertools', 'setuptools', '
setuptools._distutils.command', '_distutils_hack', 'importlib_metadata', 'importlib_metadata.compat', 'zipp'
, 'zipp.compat', 'wheel', 'setuptools._vendor.jaraco.text', 'setuptools._vendor.backports.tarfile', 'setupto
ols._vendor.backports', 'setuptools._vendor.backports.tarfile.compat', 'backports', 'importlib_resources', '
importlib_resources.future', 'importlib_resources.compat', 'setuptools.compat', 'wheel.cli', 'wheel.vendored
.packaging', 'wheel.vendored', 'setuptools.config._validate_pyproject', 'setuptools._vendor.packaging', 'tom
li', 'setuptools.config', 'setuptools.command', 'pycparser', 'pycparser.ply', 'torchvision.transforms', 'tor
ch.utils.data.datapipes.utils', 'torch.utils.data.datapipes.map', 'torch.utils.data.datapipes.iter', 'pandas
', 'pandas.core.dtypes', 'pandas.core', 'pandas.core.indexers', 'pandas.core.groupby', 'pandas.core._numba.k
ernels', 'pandas.core.window', 'pandas.util.version', 'pandas.util', 'pandas.core.util', 'pandas.core.comput
ation', 'pandas.core.array_algos', 'pyarrow.vendored', 'pyarrow', 'pyarrow.parquet', 'pyarrow.interchange', 
'Cython', 'Cython.Build', 'Cython.Distutils', 'pyximport', 'Cython.Plex', 'pygments.lexers', 'pygments', 'py
gments.styles', 'pygments.filters', 'pygments.formatters', 'Cython.Tempita', 'pkg_resources', 'platformdirs'
, 'Cython.Compiler', 'lxml', 'lxml.isoschematron', 'lxml.includes.libxslt', 'lxml.includes.libxml', 'lxml.in
cludes.libexslt', 'lxml.includes.extlibs', 'lxml.includes', 'bs4', 'soupsieve', 'bs4.builder', 'html5lib.tre
ebuilders', 'xml.dom', 'html5lib', 'html5lib.filters', 'html5lib.treewalkers', 'html5lib._trie', 'webencodin
gs', 'lxml.html', 'cssselect', 'Cython.Debugger', 'pyarrow.tests', 'pytest', 'zope.interface', 'pluggy', 'in
iconfig', '_pytest._py', '_pytest._io', 'colorama', 'exceptiongroup', '_pytest.mark', '_pytest.config', '_py
test.assertion', '_pytest._code', '_pytest', 'fsspec', 'yarl', 'propcache', 'multidict', 'aiohttp', 'attr', 
'frozenlist', 'aiosignal', 'async_timeout', 'aiohttp._websocket', 'aiohappyeyeballs', 'fsspec.implementation
s', 'lz4.frame', 'lz4', 'tqdm', 'pandas.core.tools', 'pandas.core.arrays.arrow', 'pandas.core.strings', 'pan
das.core._numba', 'pandas.core.ops', 'pandas.compat.numpy', 'pandas.core.reshape', 'scipy.interpolate', 'sci
py.optimize', 'scipy.optimize._highspy', 'scipy.optimize._shgo_lib', 'scipy.spatial', 'scipy.spatial.transfo
rm', 'scipy.constants', 'scipy.optimize._lsq', 'scipy._lib.cobyqa', 'scipy._lib.cobyqa.utils', 'scipy._lib.c
obyqa.subsolvers', 'scipy.optimize._trustregion_constr', 'scipy.optimize._trlib', 'pandas.core.indexes', 'pa
ndas.io.formats', 'pandas.io.excel', 'xlsxwriter', 'openpyxl.cell', 'openpyxl.xml', 'et_xmlfile', 'openpyxl.
descriptors', 'openpyxl.worksheet', 'openpyxl.utils', 'openpyxl.formula', 'openpyxl.compat', 'openpyxl.style
s', 'openpyxl.workbook', 'openpyxl.packaging', 'openpyxl.chartsheet', 'openpyxl.drawing', 'openpyxl.chart', 
'openpyxl.writer', 'openpyxl.comments', 'openpyxl.formatting', 'openpyxl', 'openpyxl.reader', 'openpyxl.pivo
t', 'openpyxl.workbook.external_link', 'xlrd', 'pandas.io.parsers', 'pandas.api.types', 'pytz', 'pandas.core
.arrays', 'pandas.core.arrays.sparse', 'pandas.core.methods', 'pandas.core.internals', 'pandas.io.clipboard'
, 'sqlalchemy.engine', 'sqlalchemy.cyextension', 'sqlalchemy.event', 'greenlet', 'sqlalchemy.util', 'sqlalch
emy.future', 'sqlalchemy.pool', 'sqlalchemy.ext.asyncio', 'sqlalchemy.ext', 'sqlalchemy.orm', 'sqlalchemy.di
alects', 'sqlalchemy.sql', 'sqlalchemy', 'sqlalchemy.dialects.sqlite', 'sqlalchemy.dialects.postgresql', 'sq
lalchemy.dialects.oracle', 'sqlalchemy.dialects.mysql', 'sqlalchemy.connectors', 'sqlalchemy.dialects.mssql'
, 'pandas.io.json', 'google.auth', 'google.auth.transport', 'google.oauth2', 'google.auth.compute_engine', '
cachetools', 'google.auth.crypt', 'rsa', 'pyasn1', 'pyasn1.compat', 'pyasn1.codec.ber', 'pyasn1.codec', 'pya
sn1.type', 'pyasn1.codec.cer', 'pyasn1_modules', 'pyasn1.codec.der', 'pandas.core.interchange', 'pandas._lib
s.window', 'pandas._libs.tslibs', 'pandas.io.sas', 'pandas._libs', 'pandas._testing', 'pandas.plotting', 'pa
ndas.plotting._matplotlib', 'pandas.io', 'pandas.errors', 'pandas.arrays', 'pandas.api', 'pandas.api.typing'
, 'pandas.api.interchange', 'pandas.api.indexers', 'pandas.api.extensions', 'pandas.tseries', 'pandas._confi
g', 'pandas.compat', 'torch.utils.data.datapipes.dataframe', 'torch.utils.data.datapipes', 'torch.utils.data
._utils', 'torch.utils.bottleneck', 'torch.utils.benchmark.utils.valgrind_wrapper', 'torch.utils.benchmark.u
tils', 'torch.utils.benchmark.op_fuzzers', 'torch.utils.benchmark.examples', 'torch.utils.benchmark', 'sympy
.solvers', 'sympy.multipledispatch', 'sympy.matrices.expressions', 'sympy.functions.special', 'mpmath.libmp'
, 'mpmath', 'mpmath.tests', 'mpmath.matrices', 'mpmath.calculus', 'mpmath.functions', 'sympy.external', 'sym
py.categories', 'sympy.sets', 'sympy.sets.handlers', 'sympy.physics.control', 'sympy.plotting', 'sympy.plott
ing.pygletplot', 'sympy.polys.domains', 'sympy.polys.agca', 'sympy.polys.numberfields', 'sympy.polys.matrice
s', 'sympy.codegen', 'sympy.tensor.array.expressions', 'sympy.tensor', 'sympy.parsing', 'sympy.utilities', '
sympy.strategies.branch', 'sympy.strategies', 'sympy.plotting.backends.textbackend', 'sympy.plotting.backend
s', 'sympy.plotting.backends.matplotlibbackend', 'sympy.plotting.intervalmath', 'sympy.concrete', 'sympy.phy
sics', 'sympy.physics.units', 'sympy.physics.units.systems', 'sympy.physics.units.definitions', 'sympy.funct
ions.combinatorial', 'sympy.vector', 'sympy.tensor.array', 'sympy.printing', 'sympy.utilities.mathml', 'symp
y.assumptions.relation', 'sympy.printing.pretty', 'sympy.ntheory', 'sympy.combinatorics', 'sympy.discrete', 
'sympy.testing', 'sympy.interactive', 'sympy.solvers.ode', 'sympy.solvers.diophantine', 'sympy.series', 'sym
py.calculus', 'sympy.simplify', 'sympy.geometry', 'sympy.polys', 'sympy.matrices', 'sympy.functions', 'sympy
.integrals', 'sympy.functions.elementary', 'sympy.core', 'sympy.logic.algorithms', 'sympy.assumptions.predic
ates', 'sympy.assumptions.handlers', 'sympy.assumptions', 'sympy.logic', 'sympy', 'sympy.algebras', 'torch.u
tils._sympy', 'torch.utils', 'torch.testing._internal.test_module', 'torch.testing._internal.generated', 'to
rch.testing._internal.distributed.rpc.jit', 'torch.testing._internal.distributed.rpc.examples', 'torch.testi
ng._internal.distributed.rpc', 'torch.testing._internal.distributed.pipeline', 'torch.distributed.pipeline.s
ync', 'torch.distributed.pipeline.sync.skip', 'torch.testing._internal.distributed.nn.api', 'torch.testing._
internal.distributed.nn', 'torch.testing._internal.distributed._tensor', 'torch.testing._internal.distribute
d._shard', 'torch.testing._internal.distributed', 'torch.testing._internal.data', 'torch.testing._internal.o
pinfo', 'torch.testing._internal.opinfo.definitions', 'scipy.signal', 'scipy.ndimage', 'scipy.signal.windows
', 'torchgen', 'functorch.experimental', 'functorch', 'transformers', 'safetensors', 'huggingface_hub.utils'
, 'pydantic', 'toml', 'dotenv', 'pydantic.v1', 'pydantic.plugin', 'annotated_types', 'pydantic.experimental'
, 'pydantic.deprecated', 'typing_inspection', 'zoneinfo', 'tzdata', 'tzdata.zoneinfo.US', 'tzdata.zoneinfo.P
acific', 'tzdata.zoneinfo.Mexico', 'tzdata.zoneinfo.Indian', 'tzdata.zoneinfo.Europe', 'tzdata.zoneinfo.Etc'
, 'tzdata.zoneinfo.Chile', 'tzdata.zoneinfo.Canada', 'tzdata.zoneinfo.Brazil', 'tzdata.zoneinfo.Australia', 
'tzdata.zoneinfo.Atlantic', 'tzdata.zoneinfo.Asia', 'tzdata.zoneinfo.Arctic', 'tzdata.zoneinfo.Antarctica', 
'tzdata.zoneinfo.America.North_Dakota', 'tzdata.zoneinfo.America.Kentucky', 'tzdata.zoneinfo.America.Indiana
', 'tzdata.zoneinfo.America.Argentina', 'tzdata.zoneinfo.America', 'tzdata.zoneinfo.Africa', 'tzdata.zoneinf
o', 'rich', 'markdown_it', 'markdown_it.rules_core', 'markdown_it.rules_inline', 'mdurl', 'markdown_it.commo
n', 'markdown_it.rules_block', 'markdown_it.presets', 'markdown_it.helpers', 'pydantic._internal', 'pydantic
_core', 'filelock', 'huggingface_hub.commands', 'huggingface_hub', 'huggingface_hub.serialization', 'tqdm.co
ntrib', 'huggingface_hub.inference._mcp', 'huggingface_hub.inference', 'huggingface_hub.inference._providers
', 'huggingface_hub.inference._generated.types', 'huggingface_hub.inference._generated', 'orjson', 'starlett
e', 'multipart', 'python_multipart', 'starlette.middleware', 'anyio.abc', 'anyio._core', 'sniffio', 'anyio.s
treams', 'anyio', 'anyio._backends', 'fastapi', 'fastapi.openapi', 'fastapi.dependencies', 'fastapi.security
', 'gradio', 'gradio.cli', 'gradio.cli.commands', 'typer', 'click', 'shellingham', 'tomlkit', 'semantic_vers
ion', 'gradio.cli.commands.components', 'gradio_client', 'websockets', 'websockets.sync', 'websockets.legacy
', 'websockets.extensions', 'httpx', 'httpcore', 'httpcore._backends', 'httpcore._sync', 'h11', 'httpcore._a
sync', 'httpx._transports', 'gradio_client.cli', 'openai', 'openai.resources.vector_stores', 'openai.resourc
es', 'openai.resources.responses', 'openai.types.responses', 'openai.types.shared_params', 'openai.types.sha
red', 'openai.types.chat', 'openai.resources.fine_tuning', 'openai.resources.fine_tuning.checkpoints', 'open
ai.types.fine_tuning.checkpoints', 'openai.types.fine_tuning', 'openai.types.graders', 'openai.resources.fin
e_tuning.alpha', 'openai.types.fine_tuning.alpha', 'openai.resources.fine_tuning.jobs', 'openai.types.fine_t
uning.jobs', 'openai.resources.containers', 'openai.resources.containers.files', 'openai.types.containers', 
'openai.resources.uploads', 'openai.types.uploads', 'openai.resources.evals', 'openai.resources.evals.runs',
 'openai.types.evals', 'openai.types.evals.runs', 'openai.resources.audio', 'openai.types.audio', 'openai.re
sources.chat', 'openai.resources.chat.completions', 'openai.lib.streaming.chat', 'jiter', 'openai.lib._parsi
ng', 'openai.types.chat.completions', 'openai.resources.beta', 'openai.types.beta', 'openai.types.beta.threa
ds', 'openai.types.beta.threads.runs', 'openai.resources.beta.threads', 'openai.resources.beta.threads.runs'
, 'openai.types.vector_stores', 'openai.lib.streaming.responses', 'openai._extras', 'openai.resources.beta.r
ealtime', 'openai.types.beta.realtime', 'openai.types.webhooks', 'openai.lib.streaming', 'openai.lib', 'dist
ro', 'openai._utils', 'openai.types', 'pydub', 'groovy', 'gradio.layouts', 'gradio.components', 'ffmpy', 'gr
adio.sketch', 'gradio._simple_templates', 'uvicorn.middleware', 'uvicorn', 'watchfiles', 'uvicorn.supervisor
s', 'uvicorn.protocols.websockets', 'httptools', 'httptools.parser', 'uvicorn.protocols.http', 'uvicorn.prot
ocols', 'uvicorn.loops', 'uvicorn.lifespan', 'gradio.themes', 'gradio.themes.utils', 'itsdangerous', 'safeht
tpx', 'aiofiles', 'aiofiles.tempfile', 'aiofiles.threadpool', 'torch.utils.tensorboard', 'google.protobuf.py
ext', 'google.protobuf.internal', 'google.protobuf', 'onnx', 'onnx.reference', 'onnx.reference.ops.aionnxml'
, 'onnx.reference.ops.experimental', 'onnx.reference.ops.aionnx_preview_training', 'onnx.reference.ops', 'on
nx.reference.ops_optimized', 'onnx.defs', 'datasets', 'datasets.utils', 'Crypto.Random', 'Crypto', 'Crypto.U
til', 'Crypto.Cipher', 'Crypto.Hash', 'Crypto.Protocol', 'datasets.filesystems', 'tiktoken', 'spacy', 'wasab
i', 'thinc', 'thinc.tests.shims', 'thinc.tests.regression.issue519', 'thinc.tests.regression', 'thinc.tests.
mypy.modules', 'thinc.tests.mypy', 'thinc.tests.model', 'thinc.tests.layers', 'thinc.tests.extra', 'thinc.te
sts.backends', 'thinc.tests', 'thinc.shims', 'murmurhash', 'thinc.layers', 'thinc.extra.tests', 'thinc.extra
', 'blis', 'preshed', 'cymem', 'thinc.backends', 'confection', 'catalogue', 'catalogue._importlib_metadata',
 'srsly', 'srsly.ruamel_yaml', 'srsly.cloudpickle', 'srsly.msgpack', 'srsly.ujson', 'spacy.training.converte
rs', 'spacy.training', 'spacy.tokens', 'spacy.tests.vocab_vectors', 'spacy.tests.training', 'spacy.tests.tok
enizer', 'wsgiref', 'spacy.tests.serialize', 'spacy.tests.pipeline', 'spacy.tests.parser', 'spacy.tests.pack
age', 'spacy.tests.morphology', 'spacy.tests.matcher', 'spacy.tests.lang.zh', 'spacy.tests.lang.yo', 'spacy.
tests.lang.xx', 'spacy.tests.lang.vi', 'spacy.tests.lang.ur', 'spacy.tests.lang.uk', 'spacy.tests.lang.tt', 
'spacy.tests.lang.tr', 'spacy.tests.lang.tl', 'spacy.tests.lang.ti', 'spacy.tests.lang.th', 'spacy.tests.lan
g.ta', 'spacy.tests.lang.sv', 'spacy.tests.lang.sr', 'spacy.tests.lang.sq', 'spacy.tests.lang.sl', 'spacy.te
sts.lang.sk', 'spacy.tests.lang.sa', 'spacy.tests.lang.ru', 'spacy.tests.lang.ro', 'spacy.tests.lang.pt', 's
pacy.tests.lang.pl', 'spacy.tests.lang.nn', 'spacy.tests.lang.nl', 'spacy.tests.lang.ne', 'spacy.tests.lang.
nb', 'spacy.tests.lang.ms', 'spacy.tests.lang.ml', 'spacy.tests.lang.mk', 'spacy.tests.lang.lv', 'spacy.test
s.lang.lt', 'spacy.tests.lang.lg', 'spacy.tests.lang.lb', 'spacy.tests.lang.la', 'spacy.tests.lang.ky', 'spa
cy.tests.lang.ko', 'spacy.tests.lang.kmr', 'spacy.tests.lang.ja', 'spacy.tests.lang.it', 'spacy.tests.lang.i
s', 'spacy.tests.lang.id', 'spacy.tests.lang.hy', 'spacy.tests.lang.hu', 'spacy.tests.lang.hsb', 'spacy.test
s.lang.hr', 'spacy.tests.lang.hi', 'spacy.tests.lang.he', 'spacy.tests.lang.gu', 'spacy.tests.lang.grc', 'sp
acy.tests.lang.ga', 'spacy.tests.lang.fr', 'spacy.tests.lang.fo', 'spacy.tests.lang.fi', 'spacy.tests.lang.f
a', 'spacy.tests.lang.eu', 'spacy.tests.lang.et', 'spacy.tests.lang.es', 'spacy.tests.lang.en', 'spacy.tests
.lang.el', 'spacy.tests.lang.dsb', 'spacy.tests.lang.de', 'spacy.tests.lang.da', 'spacy.tests.lang.cs', 'spa
cy.tests.lang.ca', 'spacy.tests.lang.bo', 'spacy.tests.lang.bn', 'spacy.tests.lang.ar', 'spacy.tests.lang.am
', 'spacy.tests.lang.af', 'spacy.tests.lang', 'spacy.tests.doc', 'spacy.tests', 'spacy.pipeline.legacy', 'sp
acy.pipeline._parser_internals', 'spacy.pipeline._edit_tree_internals', 'spacy.ml.models', 'spacy.ml', 'spac
y.matcher', 'spacy.lang.zh', 'jieba', 'jieba.lac_small', 'paddle.static', 'paddle.nn.initializer', 'paddle.n
n', 'paddle.utils', 'paddle.tensor', 'opt_einsum', 'opt_einsum.backends', 'paddle.framework', 'paddle.fluid.
proto', 'paddle.jit.dy2static', 'astor', 'paddle.jit', 'paddle.optimizer', 'paddle.incubate.autograd', 'padd
le.incubate', 'paddle.incubate.xpu', 'paddle.distributed.fleet', 'paddle.distributed.auto_parallel', 'paddle
.distributed.passes', 'paddle.distributed.ps.utils', 'paddle.distributed.ps', 'paddle.distributed.transpiler
', 'paddle.distributed.transpiler.details', 'paddle.incubate.distributed.fleet.parameter_server.ir', 'paddle
.incubate.distributed.fleet.parameter_server', 'paddle.amp', 'paddle.incubate.distributed.fleet', 'paddle.di
stributed.communication', 'paddle.distributed.communication.stream', 'paddle.distributed.fleet.meta_parallel
.pp_utils', 'paddle.distributed.fleet.meta_parallel', 'paddle.distributed.fleet.meta_optimizers.dygraph_opti
mizer', 'paddle.distributed.fleet.meta_optimizers', 'paddle.distributed.fleet.meta_optimizers.sharding', 'pa
ddle.distributed.fleet.meta_parallel.parallel_layers', 'paddle.incubate.nn.functional', 'paddle.distributed.
fleet.base', 'paddle.distributed.fleet.layers.mpu', 'paddle.distributed.fleet.recompute', 'paddle.incubate.d
istributed', 'paddle.static.quantization', 'paddle.io', 'paddle.io.dataloader', 'paddle.profiler', 'paddle.s
tatic.amp.bf16', 'paddle.cost_model', 'paddle.distributed.auto_parallel.static.operators', 'paddle.distribut
ed.auto_parallel.static.cost', 'paddle.distributed.auto_parallel.static', 'paddle.distributed.utils', 'paddl
e.distributed.auto_parallel.static.tuner', 'paddle.hapi', 'paddle.metric', 'paddle.distributed.fleet.utils',
 'paddle.distributed.fleet.metrics', 'paddle.distributed.fleet.data_generator', 'paddle.distributed.fleet.da
taset', 'paddle.distributed.fleet.proto', 'paddle.distributed.fleet.runtime', 'paddle.incubate.distributed.f
leet.parameter_server.distribute_transpiler', 'paddle.incubate.distributed.fleet.parameter_server.pslib', 'p
addle.distributed', 'paddle.distributed.rpc', 'paddle.distributed.launch.context', 'paddle.device', 'paddle.
device.xpu', 'paddle.device.cuda', 'paddle.distributed.launch.plugins', 'paddle.distributed.launch', 'paddle
.distributed.launch.controllers', 'paddle.distributed.launch.utils', 'paddle.distributed.launch.job', 'paddl
e.distributed.sharding', 'paddle.distributed.fleet.meta_parallel.sharding', 'paddle.distributed.fleet.elasti
c', 'paddle.incubate.layers', 'paddle.incubate.nn', 'paddle.incubate.multiprocessing', 'paddle.incubate.asp'
, 'paddle.incubate.passes', 'paddle.incubate.tensor', 'paddle.incubate.operators', 'paddle.incubate.checkpoi
nt', 'paddle.fluid.incubate.checkpoint', 'paddle.fluid.incubate', 'paddle.incubate.optimizer', 'paddle.incub
ate.optimizer.functional', 'paddle.autograd', 'paddle.reader', 'paddle.utils.cpp_extension', 'paddle.utils.g
ast', 'paddle.nn.quant', 'paddle.nn.quant.qat', 'paddle.nn.utils', 'paddle.nn.layer', 'paddle.nn.functional'
, 'paddle.fluid.layers', 'proto', 'proto.marshal', 'proto.marshal.rules', 'proto.marshal.collections', 'padd
le.static.nn', 'paddle.static.amp', 'paddle.fluid.dygraph', 'paddle.version', 'paddle', 'paddle.text', 'padd
le.text.datasets', 'paddle.quantization', 'paddle.quantization.imperative', 'paddle.sparse', 'paddle.sparse.
nn', 'paddle.sparse.nn.functional', 'paddle.geometric', 'paddle.geometric.sampling', 'paddle.geometric.messa
ge_passing', 'paddle.audio', 'paddle.audio.backends', 'paddle.audio.datasets', 'paddle.audio.functional', 'p
addle.audio.features', 'paddle.vision', 'paddle.vision.datasets', 'paddle.vision.transforms', 'paddle.vision
.models', 'paddle.onnx', 'paddle.inference', 'paddle.dataset', 'cv2', 'cv2.utils', 'cv2.typing', 'cv2.misc',
 'cv2.mat_wrapper', 'cv2.gapi', 'cv2.data', 'paddle.distribution', 'paddle.fluid', 'paddle.fluid.contrib', '
paddle.fluid.contrib.extend_optimizer', 'jieba.finalseg', 'spacy.lang.yo', 'spacy.lang.xx', 'spacy.lang.vi',
 'spacy.lang.ur', 'spacy.lang.uk', 'spacy.lang.tt', 'spacy.lang.tr', 'spacy.lang.tn', 'spacy.lang.tl', 'spac
y.lang.ti', 'spacy.lang.th', 'spacy.lang.te', 'spacy.lang.ta', 'spacy.lang.sv', 'spacy.lang.sr', 'spacy.lang
.sq', 'spacy.lang.sl', 'spacy.lang.sk', 'spacy.lang.si', 'spacy.lang.sa', 'spacy.lang.ru', 'spacy.lang.ro', 
'spacy.lang.pt', 'spacy.lang.pl', 'spacy.lang.nn', 'spacy.lang.nl', 'spacy.lang.ne', 'spacy.lang.nb', 'spacy
.lang.ms', 'spacy.lang.mr', 'spacy.lang.ml', 'spacy.lang.mk', 'spacy.lang.lv', 'spacy.lang.lt', 'spacy.lang.
lij', 'spacy.lang.lg', 'spacy.lang.lb', 'spacy.lang.la', 'spacy.lang.ky', 'spacy.lang.ko', 'spacy.lang.kn', 
'spacy.lang.kmr', 'spacy.lang.ja', 'spacy.lang.it', 'spacy.lang.is', 'spacy.lang.id', 'spacy.lang.hy', 'spac
y.lang.hu', 'spacy.lang.hsb', 'spacy.lang.hr', 'spacy.lang.hi', 'spacy.lang.he', 'spacy.lang.gu', 'spacy.lan
g.grc', 'spacy.lang.gd', 'spacy.lang.ga', 'spacy.lang.fr', 'spacy.lang.fo', 'spacy.lang.fi', 'spacy.lang.fa'
, 'spacy.lang.eu', 'spacy.lang.et', 'spacy.lang.es', 'spacy.lang.en', 'spacy.lang.el', 'spacy.lang.dsb', 'sp
acy.lang.de', 'spacy.lang.da', 'spacy.lang.cs', 'spacy.lang.ca', 'spacy.lang.bo', 'spacy.lang.bn', 'spacy.la
ng.bg', 'spacy.lang.az', 'spacy.lang.ar', 'spacy.lang.am', 'spacy.lang.af', 'spacy.lang', 'spacy.kb', 'wease
l.cli', 'cloudpathlib', 'cloudpathlib.s3', 'google.api_core.retry', 'grpc.experimental.aio', 'grpc.aio', 'gr
pc._cython', 'grpc.experimental', 'grpc_status', 'grpc', 'google.api_core', 'cloudpathlib.gs', 'cloudpathlib
.azure', 'weasel', 'weasel.util', 'smart_open', 'wrapt', 'spacy.cli.project', 'spacy.cli', 'spacy.displacy',
 'spacy.pipeline', 'langcodes', 'language_data', 'datasets.parallel', 'joblib', 'joblib.externals.loky', 'jo
blib.externals.loky.backend', 'joblib.externals.cloudpickle', 'joblib.externals', 'multiprocess.dummy', '_mu
ltiprocess', 'test.support', 'test', 'multiprocess', 'pyarrow_hotfix', 'datasets.tasks', 'datasets.packaged_
modules', 'cbor', 'datasets.packaged_modules.webdataset', 'datasets.packaged_modules.text', 'datasets.packag
ed_modules.sql', 'datasets.packaged_modules.parquet', 'datasets.packaged_modules.pandas', 'datasets.packaged
_modules.json', 'datasets.packaged_modules.folder_based_builder', 'datasets.packaged_modules.imagefolder', '
datasets.packaged_modules.csv', 'datasets.packaged_modules.cache', 'datasets.packaged_modules.audiofolder', 
'datasets.packaged_modules.arrow', 'datasets.io', 'datasets.packaged_modules.spark', 'datasets.packaged_modu
les.generator', 'datasets.formatting', 'xxhash', 'datasets.features', 'datasets.download', 'faiss', 'transfo
rmers.benchmark', 'accelerate.utils', 'accelerate.commands.config', 'accelerate.commands', 'accelerate.comma
nds.menu', 'peft', 'peft.utils', 'auto_gptq.utils', 'auto_gptq', 'auto_gptq.nn_modules.qlinear', 'auto_gptq.
nn_modules', 'auto_gptq.quantization', 'auto_gptq.nn_modules.triton_utils', 'peft.tuners.lora', 'bitsandbyte
s', 'bitsandbytes.optim', 'bitsandbytes.nn', 'bitsandbytes.triton', 'bitsandbytes.autograd', 'bitsandbytes.r
esearch', 'bitsandbytes.research.autograd', 'bitsandbytes.research.nn', 'peft.tuners.adalora', 'auto_gptq.mo
deling', 'transformers.onnx', 'onnxruntime', 'onnxruntime.capi.training', 'onnxruntime.capi', 'gekko', 'flas
k', 'werkzeug.sansio', 'werkzeug', 'watchdog.observers', 'watchdog', 'watchdog.utils', 'werkzeug.debug', 'we
rkzeug.middleware', 'werkzeug.datastructures', 'werkzeug.routing', 'werkzeug.wrappers', 'blinker', 'asgiref'
, 'flask.json', 'peft.tuners.trainable_tokens', 'peft.tuners', 'peft.tuners.xlora', 'peft.tuners.vera', 'pef
t.tuners.vblora', 'peft.tuners.prompt_tuning', 'peft.tuners.prefix_tuning', 'peft.tuners.poly', 'peft.tuners
.p_tuning', 'peft.tuners.multitask_prompt_tuning', 'peft.tuners.mixed', 'peft.tuners.oft', 'peft.tuners.lokr
', 'peft.tuners.loha', 'peft.tuners.ln_tuning', 'peft.tuners.ia3', 'peft.tuners.hra', 'peft.tuners.fourierft
', 'peft.tuners.cpt', 'peft.tuners.bone', 'peft.tuners.boft', 'peft.tuners.adaption_prompt', 'accelerate', '
optimum.bettertransformer', 'optimum.utils', 'optimum.bettertransformer.models', 'transformers.quantizers', 
'optimum.gptq', 'transformers.data.datasets', 'transformers.data.processors', 'tokenizers.models', 'tokenize
rs.processors', 'tokenizers.pre_tokenizers', 'tokenizers.normalizers', 'tokenizers.decoders', 'tokenizers', 
'tokenizers.implementations', 'tokenizers.trainers', 'transformers.models.mbart50', 'transformers.models', '
transformers.models.megatron_gpt2', 'transformers.models.dit', 'transformers.models.dialogpt', 'transformers
.models.deprecated', 'transformers.models.nllb', 'transformers.models.mluke', 'transformers.models.gpt_sw3',
 'transformers.models.cpm', 'transformers.models.code_llama', 'transformers.models.bartpho', 'transformers.m
odels.barthez', 'transformers.models.zoedepth', 'transformers.models.yoso', 'transformers.models.yolos', 'tr
ansformers.models.xmod', 'transformers.models.xlnet', 'transformers.models.xlm_roberta_xl', 'transformers.mo
dels.xlm_roberta', 'transformers.models.xlm', 'transformers.models.xglm', 'transformers.models.x_clip', 'tra
nsformers.models.whisper', 'transformers.models.wavlm', 'transformers.models.wav2vec2_with_lm', 'transformer
s.models.wav2vec2_phoneme', 'transformers.models.wav2vec2_conformer', 'transformers.models.wav2vec2_bert', '
transformers.models.wav2vec2', 'transformers.models.vivit', 'transformers.models.vits', 'transformers.models
.vitmatte', 'transformers.models.vitdet', 'transformers.models.vit_msn', 'transformers.models.vit_mae', 'tra
nsformers.models.vit', 'transformers.models.visual_bert', 'transformers.models.vision_text_dual_encoder', 't
ransformers.models.vision_encoder_decoder', 'transformers.models.vipllava', 'transformers.models.vilt', 'tra
nsformers.models.videomae', 'transformers.models.video_llava', 'transformers.models.upernet', 'transformers.
models.univnet', 'transformers.models.unispeech_sat', 'transformers.models.unispeech', 'transformers.models.
umt5', 'transformers.models.udop', 'transformers.models.tvp', 'transformers.models.trocr', 'transformers.mod
els.timm_backbone', 'timm', 'timm.models', 'timm.data', 'timm.data.readers', 'timm.utils', 'timm.layers', 't
ransformers.models.timesformer', 'transformers.models.time_series_transformer', 'transformers.models.tapas',
 'transformers.models.table_transformer', 'transformers.models.t5', 'transformers.models.switch_transformers
', 'transformers.models.swinv2', 'transformers.models.swin2sr', 'transformers.models.swin', 'transformers.mo
dels.swiftformer', 'transformers.models.superpoint', 'transformers.models.starcoder2', 'transformers.models.
stablelm', 'transformers.models.squeezebert', 'transformers.models.splinter', 'transformers.models.speecht5'
, 'transformers.models.speech_to_text', 'transformers.models.speech_encoder_decoder', 'transformers.models.s
iglip', 'transformers.models.sew_d', 'transformers.models.sew', 'transformers.models.seggpt', 'transformers.
models.segformer', 'transformers.models.seamless_m4t_v2', 'transformers.models.seamless_m4t', 'transformers.
models.sam', 'transformers.models.rwkv', 'transformers.models.rt_detr', 'transformers.models.roformer', 'tra
nsformers.models.roc_bert', 'transformers.models.roberta_prelayernorm', 'transformers.models.roberta', 'tran
sformers.models.resnet', 'transformers.models.rembert', 'transformers.models.regnet', 'transformers.models.r
eformer', 'transformers.models.recurrent_gemma', 'transformers.models.rag', 'transformers.models.qwen2_moe',
 'transformers.models.qwen2', 'transformers.models.pvt_v2', 'transformers.models.pvt', 'transformers.models.
prophetnet', 'transformers.models.pop2piano', 'transformers.models.poolformer', 'transformers.models.plbart'
, 'transformers.models.pix2struct', 'transformers.models.phobert', 'transformers.models.phi3', 'transformers
.models.phi', 'transformers.models.persimmon', 'transformers.models.perceiver', 'transformers.models.pegasus
_x', 'transformers.models.pegasus', 'transformers.models.patchtst', 'transformers.models.patchtsmixer', 'tra
nsformers.models.paligemma', 'transformers.models.owlvit', 'transformers.models.owlv2', 'transformers.models
.opt', 'transformers.models.openai', 'ftfy', 'ftfy.bad_codecs', 'transformers.models.oneformer', 'transforme
rs.models.olmo', 'transformers.models.nystromformer', 'transformers.models.nougat', 'transformers.models.nll
b_moe', 'transformers.models.nemotron', 'transformers.models.mvp', 'transformers.models.musicgen_melody', 't
ransformers.models.musicgen', 'transformers.models.mt5', 'transformers.models.mra', 'transformers.models.mpt
', 'transformers.models.mpnet', 'transformers.models.mobilevitv2', 'transformers.models.mobilevit', 'transfo
rmers.models.mobilenet_v2', 'transformers.models.mobilenet_v1', 'transformers.models.mobilebert', 'transform
ers.models.mixtral', 'transformers.models.mistral', 'transformers.models.mgp_str', 'transformers.models.mega
tron_bert', 'transformers.models.mbart', 'transformers.models.maskformer', 'transformers.models.mask2former'
, 'transformers.models.markuplm', 'transformers.models.marian', 'transformers.models.mamba2', 'transformers.
models.mamba', 'transformers.models.m2m_100', 'transformers.models.lxmert', 'transformers.models.luke', 'tra
nsformers.models.longt5', 'transformers.models.longformer', 'transformers.models.llava_next_video', 'transfo
rmers.models.llava_next', 'transformers.models.llava', 'transformers.models.llama', 'transformers.models.lil
t', 'transformers.models.levit', 'transformers.models.led', 'transformers.models.layoutxlm', 'transformers.m
odels.layoutlmv3', 'pytesseract', 'transformers.models.layoutlmv2', 'transformers.models.layoutlm', 'transfo
rmers.models.kosmos2', 'transformers.models.jetmoe', 'transformers.models.jamba', 'transformers.models.instr
uctblipvideo', 'transformers.models.instructblip', 'transformers.models.informer', 'transformers.models.imag
egpt', 'transformers.models.idefics2', 'transformers.models.idefics', 'transformers.models.ibert', 'transfor
mers.models.hubert', 'transformers.models.hiera', 'transformers.models.herbert', 'transformers.models.groupv
it', 'transformers.models.grounding_dino', 'transformers.models.gptj', 'transformers.models.gpt_neox_japanes
e', 'transformers.models.gpt_neox', 'transformers.models.gpt_neo', 'transformers.models.gpt_bigcode', 'trans
formers.models.gpt2', 'transformers.models.glpn', 'transformers.models.git', 'transformers.models.gemma2', '
transformers.models.gemma', 'transformers.models.fuyu', 'transformers.models.funnel', 'transformers.models.f
smt', 'transformers.models.focalnet', 'transformers.models.fnet', 'transformers.models.flava', 'transformers
.models.flaubert', 'transformers.models.fastspeech2_conformer', 'transformers.models.falcon', 'transformers.
models.esm', 'transformers.models.esm.openfold_utils', 'transformers.models.ernie', 'transformers.models.enc
oder_decoder', 'transformers.models.encodec', 'transformers.models.electra', 'transformers.models.efficientn
et', 'transformers.models.dpt', 'transformers.models.dpr', 'transformers.models.donut', 'transformers.models
.distilbert', 'transformers.models.dinov2', 'transformers.models.dinat', 'transformers.models.detr', 'transf
ormers.models.depth_anything', 'transformers.models.deprecated.xlm_prophetnet', 'transformers.models.depreca
ted.vit_hybrid', 'transformers.models.deprecated.van', 'transformers.models.deprecated.tvlt', 'transformers.
models.deprecated.transfo_xl', 'transformers.models.deprecated.trajectory_transformer', 'transformers.models
.deprecated.tapex', 'transformers.models.deprecated.speech_to_text_2', 'transformers.models.deprecated.retri
bert', 'transformers.models.deprecated.realm', 'transformers.models.deprecated.qdqbert', 'transformers.model
s.deprecated.open_llama', 'transformers.models.deprecated.nezha', 'transformers.models.deprecated.nat', 'tra
nsformers.models.deprecated.mmbt', 'transformers.models.deprecated.mega', 'transformers.models.deprecated.mc
tct', 'transformers.models.deprecated.jukebox', 'transformers.models.deprecated.graphormer', 'transformers.m
odels.deprecated.gptsan_japanese', 'transformers.models.deprecated.ernie_m', 'transformers.models.deprecated
.efficientformer', 'transformers.models.deprecated.deta', 'transformers.models.deit', 'transformers.models.d
eformable_detr', 'transformers.models.decision_transformer', 'transformers.models.deberta_v2', 'transformers
.models.deberta', 'transformers.models.dbrx', 'transformers.models.data2vec', 'transformers.models.cvt', 'tr
ansformers.models.ctrl', 'transformers.models.cpmant', 'transformers.models.convnextv2', 'transformers.model
s.convnext', 'transformers.models.convbert', 'transformers.models.conditional_detr', 'transformers.models.co
here', 'transformers.models.codegen', 'transformers.models.clvp', 'transformers.models.clipseg', 'transforme
rs.models.clip', 'transformers.models.clap', 'transformers.models.chinese_clip', 'transformers.models.chamel
eon', 'transformers.models.canine', 'transformers.models.camembert', 'transformers.models.byt5', 'transforme
rs.models.bros', 'transformers.models.bridgetower', 'transformers.models.bloom', 'transformers.models.blip_2
', 'transformers.models.blip', 'transformers.models.blenderbot_small', 'transformers.models.blenderbot', 'tr
ansformers.models.bit', 'transformers.models.biogpt', 'transformers.models.bigbird_pegasus', 'transformers.m
odels.big_bird', 'transformers.models.bertweet', 'emoji', 'emoji.unicode_codes', 'transformers.models.bert_j
apanese', 'transformers.models.bert_generation', 'transformers.models.bert', 'transformers.models.beit', 'tr
ansformers.models.bart', 'transformers.models.bark', 'transformers.models.autoformer', 'transformers.models.
auto', 'transformers.models.audio_spectrogram_transformer', 'transformers.models.altclip', 'transformers.mod
els.align', 'transformers.models.albert', 'transformers.integrations', 'transformers.generation', 'transform
ers.data', 'transformers.data.metrics', 'sklearn.metrics', 'sklearn.metrics._pairwise_distances_reduction', 
'sklearn.utils', 'sklearn.externals._packaging', 'sklearn.externals', 'sklearn.preprocessing', 'sklearn.mode
l_selection', 'sklearn.cluster', 'sklearn.neighbors', 'sklearn.tree', 'sklearn.linear_model', 'sklearn.svm',
 'sklearn._loss', 'sklearn.linear_model._glm', 'sklearn.covariance', 'sklearn.decomposition', 'sklearn.manif
old', 'sklearn.cluster._hdbscan', 'scipy.cluster', 'sklearn.gaussian_process', 'sklearn.metrics._plot', 'skl
earn.metrics.cluster', 'sklearn', 'sklearn.__check_build', 'transformers.agents', 'transformers.utils', 'tra
nsformers.pipelines', 'torch.testing._internal.codegen', 'torch.testing._internal', 'torch.quantization.fx',
 'torch.package.analyze', 'torch.package', 'functorch.compile', 'torch.onnx._internal.fx.passes', 'torch.onn
x._internal.fx', 'torch.onnx._internal.fx.analysis', 'torch.onnx._internal.diagnostics.infra.sarif', 'torch.
onnx._internal.diagnostics.infra', 'torch.onnx._internal.diagnostics', 'torch.onnx._internal', 'torch.nn.uti
ls._expanded_weights', 'torch.nn.utils', 'torch.nn.quantized.modules', 'torch.nn.quantized.dynamic.modules',
 'torch.nn.quantized.dynamic', 'torch.nn.quantized._reference.modules', 'torch.nn.quantized._reference', 'to
rch.nn.quantizable.modules', 'torch.nn.qat.modules', 'torch.nn.qat.dynamic.modules', 'torch.nn.qat.dynamic',
 'torch.nn.parallel', 'torch.nn.modules', 'torch.nn.intrinsic.quantized.modules', 'torch.nn.intrinsic.quanti
zed.dynamic.modules', 'torch.nn.intrinsic.quantized.dynamic', 'torch.nn.intrinsic.quantized', 'torch.nn.intr
insic.qat.modules', 'torch.nn.intrinsic.qat', 'torch.nn.intrinsic.modules', 'torch.nn.backends', 'torch.moni
tor', 'torch.masked.maskedtensor', 'torch.jit.mobile', 'torch.jit._passes', 'torch.fx.passes.utils', 'torch.
fx.passes.tests', 'torch.fx.passes.infra', 'torch.fx.passes.dialect.common', 'torch.fx.passes.dialect', 'tor
ch.fx.passes.backends', 'torch.fx.passes', 'torch.fx.experimental.unification.multipledispatch', 'torch.fx.e
xperimental.unification', 'torch.fx.experimental.migrate_gradual_types', 'torch.fx.experimental', 'torch.fx'
, 'torch.distributed.tensor.parallel', 'torch.distributed.tensor', 'torch.distributed.rpc._testing', 'torch.
distributed.rpc', 'torch.distributed.pipeline', 'torch.distributed.optim', 'torch.distributed.nn.jit.templat
es', 'torch.distributed.nn.jit', 'torch.distributed.nn.api', 'torch.distributed.nn', 'torch.distributed.laun
cher', 'torch.distributed.fsdp', 'torch.distributed.elastic.utils.data', 'torch.distributed.elastic.utils', 
'torch.distributed.elastic.timer', 'torch.distributed.elastic.rendezvous', 'torch.distributed.elastic.multip
rocessing.errors', 'torch.distributed.elastic.multiprocessing', 'torch.distributed.elastic.metrics', 'torch.
distributed.elastic.events', 'torch.distributed.elastic.agent.server', 'torch.distributed.elastic.agent', 't
orch.distributed.elastic', 'torch.distributed.checkpoint', 'torch.distributed.autograd', 'torch.distributed.
algorithms.model_averaging', 'torch.distributed.algorithms.ddp_comm_hooks', 'torch.distributed.algorithms._q
uantization', 'torch.distributed.algorithms._optimizer_overlap', 'torch.distributed.algorithms._comm_hooks',
 'torch.distributed.algorithms._checkpoint', 'torch.distributed.algorithms', 'torch.distributed._tools', 'to
rch.distributed._tensor.ops', 'torch.distributed._tensor.debug', 'torch.distributed._tensor', 'torch.distrib
uted._spmd', 'torch.distributed._sharding_spec', 'torch.distributed._sharded_tensor', 'torch.distributed._sh
ard.sharding_spec.chunk_sharding_spec_ops', 'torch.distributed._shard.sharding_spec', 'torch.distributed._sh
ard.sharding_plan', 'torch.distributed._shard.sharded_tensor._ops', 'torch.distributed._shard.sharded_tensor
', 'torch.distributed._shard.sharded_optim', 'torch.distributed._shard.checkpoint', 'torch.distributed._shar
d', 'torch.distributed._composable', 'torch.distributed', 'torch.cuda.amp', 'torch.cpu.amp', 'torch.contrib'
, 'torch.backends.xnnpack', 'torch.backends.xeon', 'torch.backends.quantized', 'torch.backends.opt_einsum', 
'torch.backends.openmp', 'torch.backends.mps', 'torch.backends.mkldnn', 'torch.backends.mkl', 'torch.backend
s.cudnn', 'torch.backends.cuda', 'torch.backends.cpu', 'torch.backends._nnapi', 'torch.backends._coreml', 't
orch.autograd._functions', 'torch.ao.quantization.quantizer', 'torch.ao.quantization.pt2e.representation', '
torch.ao.quantization.pt2e', 'torch.ao.quantization.fx._model_report', 'torch.ao.quantization.fx', 'torch.ao
.quantization.backend_config', 'torch.ao.quantization', 'torch.ao.pruning.sparsifier', 'torch.ao.pruning.sch
eduler', 'torch.ao.pruning._experimental.pruner', 'torch.ao.pruning._experimental.data_sparsifier.lightning.
callbacks', 'torch.ao.pruning._experimental.data_sparsifier.lightning', 'torch.ao.pruning._experimental.data
_sparsifier', 'torch.ao.pruning._experimental.data_scheduler', 'torch.ao.pruning._experimental.activation_sp
arsifier', 'torch.ao.pruning._experimental', 'torch.ao.pruning', 'torch.ao.ns.fx', 'torch.ao.ns', 'torch.ao.
nn.sparse.quantized.dynamic', 'torch.ao.nn.sparse.quantized', 'torch.ao.nn.sparse', 'torch.ao.nn.quantized.r
eference.modules', 'torch.ao.nn.quantized.reference', 'torch.ao.nn.quantized.modules', 'torch.ao.nn.quantize
d.dynamic.modules', 'torch.ao.nn.quantized.dynamic', 'torch.ao.nn.quantized', 'torch.ao.nn.quantizable.modul
es', 'torch.ao.nn.quantizable', 'torch.ao.nn.qat.modules', 'torch.ao.nn.qat.dynamic.modules', 'torch.ao.nn.q
at.dynamic', 'torch.ao.nn.qat', 'torch.ao.nn.intrinsic.quantized.modules', 'torch.ao.nn.intrinsic.quantized.
dynamic.modules', 'torch.ao.nn.intrinsic.quantized.dynamic', 'torch.ao.nn.intrinsic.quantized', 'torch.ao.nn
.intrinsic.qat.modules', 'torch.ao.nn.intrinsic.qat', 'torch.ao.nn.intrinsic.modules', 'torch.ao.nn.intrinsi
c', 'torch.ao.nn', 'torch._subclasses', 'torch._refs.special', 'torch._refs.nn.functional', 'torch._refs.nn'
, 'torch._refs.linalg', 'torch._prims_common', 'torch._prims', 'torch._numpy.testing', 'torch._numpy', 'torc
h._lazy', 'torch._inductor.kernel', 'torch._inductor.fx_passes', 'torch._inductor.codegen', 'torch._higher_o
rder_ops', 'torch._functorch', 'torch._export.serde', 'torch._export.passes', 'torch._export.pass_infra', 't
orch._export.db.examples', 'torch._export.db', 'torch._export', 'torch._dynamo.variables', 'torch._dynamo.re
pro', 'einops.layers', 'einops', 'torch._dynamo.backends', 'torch._dispatch', 'torch._decomp', 'torchgen.api
.types', 'torchgen.api', 'torch._custom_op', 'win32com.shell', 'win32com', 'win32com.client', 'pywin.mfc', '
pywin.dialogs', 'pywin', 'win32com.server', 'torch._logging', 'torch.onnx', 'torch._dynamo', 'torch.compiler
', 'torch.func', 'torch.export', 'torch._inductor', 'torch.masked', 'torch.quantization', 'torch.nn.intrinsi
c', 'torch.nn.qat', 'torch.nn.quantized', 'torch.nn.quantizable', 'torch.ao', 'torch.profiler', 'torch.utils
.data', 'torch.backends', 'torch.testing', 'torch.distributions', 'torch.linalg', 'torch.jit', 'torch.utils.
backcompat', 'torch.special', 'torch.sparse', 'torch.multiprocessing', 'torch.optim._multi_tensor', 'torch.o
ptim', 'torch.signal.windows', 'torch.signal', 'torch.nn', 'torch.nested', 'torch._awaits', 'torch.futures',
 'torch.fft', 'torch.autograd', 'torch.mps', 'torch.cpu', 'torch.cuda', 'torch.amp', 'torch._refs', 'scipy._
lib.array_api_compat.cupy', 'scipy._lib.array_api_compat.dask.array', 'scipy._lib.array_api_compat.dask', 's
cipy._lib.array_api_compat.common', 'scipy.integrate', 'scipy.integrate._rules', 'scipy.integrate._ivp', 'sc
ipy.stats', 'scipy.stats._rcont', 'scipy.stats._levy_stable', 'scipy._lib', 'networkx.linalg', 'networkx.alg
orithms', 'networkx.algorithms.tree', 'networkx.algorithms.isomorphism', 'networkx.algorithms.flow', 'networ
kx.algorithms.community', 'networkx.algorithms.connectivity', 'networkx.algorithms.bipartite', 'networkx.alg
orithms.approximation', 'networkx.algorithms.traversal', 'networkx.algorithms.shortest_paths', 'networkx.alg
orithms.operators', 'networkx.algorithms.minors', 'networkx.algorithms.link_analysis', 'networkx.algorithms.
coloring', 'networkx.algorithms.components', 'networkx.algorithms.centrality', 'networkx.algorithms.assortat
ivity', 'networkx.readwrite', 'networkx.readwrite.json_graph', 'networkx.generators', 'networkx.classes', 'n
etworkx.utils', 'nltk.tree', 'nltk.translate', 'nltk.tokenize', 'nltk.tag', 'nltk.parse', 'sklearn.datasets'
, 'sklearn.feature_extraction', 'nltk.metrics', 'nltk.inference', 'nltk.classify', 'nltk.chunk', 'nltk.lm', 
'nltk.corpus', '_pyi_rth_utils', 'encodings', 'collections', 'src.ui', 'src', 'fitz', 'fontTools.subset', 'f
ontTools.misc', 'fontTools.misc.plistlib', 'fontTools.encodings', 'fontTools.pens', 'fontTools.colorLib', 'f
ontTools.unicodedata', 'fontTools.designspaceLib', 'fontTools.varLib', 'fontTools.cffLib', 'fontTools.otlLib
.optimize', 'fontTools.otlLib', 'fontTools.feaLib', 'fontTools.ttLib.tables', 'lz4.block', 'fontTools.ttLib'
, 'fontTools.config', 'fontTools', 'src.core', 'src.utils', 'pdf2image', 'unstructured.partition', 'unstruct
ured', 'pypandoc', 'unstructured.nlp', 'langdetect', 'langdetect.utils', 'iso639', 'iso639._data', 'unstruct
ured_inference', 'unstructured_inference.inference', 'unstructured_inference.models', 'layoutparser.elements
', 'layoutparser', 'pdfplumber', 'pypdfium2', 'pypdfium2.internal', 'pypdfium2_raw', 'pypdfium2._helpers', '
pdfplumber.utils', 'unstructured_inference.patches', 'onnxruntime.quantization', 'onnxruntime.tools', 'onnxr
untime.quantization.operators', 'onnxruntime.quantization.CalTableFlatBuffers', 'flatbuffers', 'layoutparser
.models', 'layoutparser.models.effdet', 'effdet.data', 'effdet.data.parsers', 'effdet.object_detection', 'ef
fdet', 'effdet.config', 'omegaconf', 'omegaconf.resolvers.oc', 'omegaconf.grammar.gen', 'omegaconf.grammar',
 'antlr4.error', 'antlr4.tree', 'antlr4.atn', 'antlr4.dfa', 'antlr4', 'antlr4.xpath', 'omegaconf.resolvers',
 'effdet.evaluation', 'timm.models.layers', 'iopath.common', 'iopath', 'iopath.tabular', 'portalocker', 'lay
outparser.models.paddledetection', 'layoutparser.models.detectron2', 'pdfminer', 'unstructured.partition.uti
ls', 'pptx', 'pptx.oxml.dml', 'pptx.enum', 'pptx.oxml.shapes', 'pptx.oxml', 'pptx.oxml.chart', 'pptx.dml', '
pptx.chart', 'pptx.parts', 'pptx.opc', 'pptx.text', 'pptx.shapes', 'unstructured.documents', 'unstructured.f
ile_utils', 'filetype', 'filetype.types', 'magic', 'unstructured.cleaners', 'unstructured.chunking', 'docx.t
ext', 'docx.oxml', 'docx.oxml.text', 'docx.enum', 'docx.dml', 'docx.styles', 'docx.opc', 'docx.opc.parts', '
docx.parts', 'docx.image', 'docx.drawing', 'unstructured.partition.utils.ocr_models', 'unstructured_pytesser
act', 'docx', 'jieba.analyse', 'whoosh.lang', 'whoosh.lang.snowball', 'jieba.posseg', 'whoosh.util', 'whoosh
.filedb', 'whoosh.analysis', 'whoosh.support', 'whoosh.query', 'whoosh.matching', 'whoosh.automata', 'whoosh
.codec', 'whoosh.qparser', 'whoosh', 'optimum.onnxruntime', 'optimum.onnx', 'onnxruntime.transformers', 'opt
imum.exporters.onnx', 'optimum.commands.export', 'optimum.commands', 'optimum.exporters.tflite', 'optimum.ut
ils.preprocessing', 'optimum.exporters', 'optimum.onnxruntime.preprocessors', 'coloredlogs', 'humanfriendly.
terminal', 'humanfriendly', 'onnx.tools', 'sentence_transformers.losses', 'sentence_transformers.cross_encod
er.losses', 'sentence_transformers.readers', 'sentence_transformers.datasets', 'sentence_transformers.cross_
encoder', 'sentence_transformers.models', 'sentence_transformers.models.tokenizer', 'sentence_transformers.e
valuation', 'sentence_transformers', 'overrides', 'chromadb', 'chromadb.utils', 'tenacity', 'tenacity.asynci
o', 'chromadb.api', 'chromadb.auth.token', 'chromadb.telemetry.opentelemetry', 'chromadb.telemetry', 'opente
lemetry.exporter.otlp.proto.grpc.trace_exporter', 'opentelemetry.exporter.otlp.proto.grpc', 'opentelemetry.s
dk.environment_variables', 'opentelemetry.sdk.util', 'deprecated', 'opentelemetry.proto.trace.v1', 'opentele
metry.proto.trace', 'opentelemetry.proto', 'opentelemetry.proto.resource.v1', 'opentelemetry.proto.resource'
, 'opentelemetry.proto.common.v1', 'opentelemetry.proto.common', 'opentelemetry.proto.collector.trace.v1', '
opentelemetry.proto.collector.trace', 'opentelemetry.proto.collector', 'opentelemetry.sdk.metrics.export', '
opentelemetry.sdk.metrics', 'opentelemetry.attributes', 'opentelemetry.metrics._internal', 'opentelemetry.en
vironment_variables', 'opentelemetry.metrics', 'opentelemetry.sdk.metrics._internal', 'opentelemetry.sdk.met
rics._internal.exponential_histogram.mapping', 'opentelemetry.sdk.metrics._internal.export', 'opentelemetry.
context', 'opentelemetry.exporter.otlp.proto.common._internal', 'opentelemetry.exporter.otlp.proto.common', 
'opentelemetry.exporter.otlp.proto.common._internal.trace_encoder', 'opentelemetry.sdk.trace.export', 'opent
elemetry.sdk.trace', 'opentelemetry.semconv', 'opentelemetry.sdk.resources', 'opentelemetry.semconv.resource
', 'opentelemetry.trace', 'opentelemetry.trace.propagation', 'opentelemetry.exporter.otlp.proto.grpc.metric_
exporter', 'opentelemetry.exporter.otlp.proto.common._internal.metrics_encoder', 'opentelemetry.sdk.metrics.
view', 'opentelemetry.proto.metrics.v1', 'opentelemetry.proto.metrics', 'opentelemetry.proto.collector.metri
cs.v1', 'opentelemetry.proto.collector.metrics', 'opentelemetry.sdk._logs.export', 'opentelemetry.sdk._logs.
_internal.export', 'opentelemetry.sdk._logs._internal', 'opentelemetry.semconv.trace', 'opentelemetry._logs'
, 'opentelemetry._logs.severity', 'opentelemetry._logs._internal', 'opentelemetry.baggage.propagation', 'ope
ntelemetry.baggage', 'opentelemetry.exporter.otlp.proto.grpc._log_exporter', 'opentelemetry.exporter.otlp.pr
oto.common._internal._log_encoder', 'opentelemetry.sdk._logs', 'chromadb.auth', 'chromadb.telemetry.product'
, 'loguru', 'win32_setctime', 'multiprocessing']
PS D:\LocalQA>
```

---

_**User**_

pyinstaller localqa.spec已完成，请执行下一步

---

_**Assistant**_




---

<details>
            <summary>Listed directory dist • **0** results</summary>
        
No results found
</details>

---

```bash
dir dist
```

```
PS D:\LocalQA> dir dist
PS D:\LocalQA>
```

---

```bash
pyinstaller localqa.spec --clean
```

```
147 INFO: PyInstaller: 6.14.1, contrib hooks: 2025.5
147 INFO: Python: 3.10.11
151 INFO: Platform: Windows-10-10.0.22631-SP0
151 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python310
153 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
154 INFO: Module search paths (PYTHONPATH):
['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Scripts\\pyinstaller.exe',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.zip',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor',  
 'D:\\LocalQA',
 'D:\\LocalQA']
194 WARNING: discover_hook_directories: Failed to process hook entry point 'EntryPoint(name='hook-dirs', val
ue='rapidfuzz.__pyinstaller:get_hook_dirs', group='pyinstaller40')': AttributeError: module 'rapidfuzz.__pyi
nstaller' has no attribute 'get_hook_dirs'
457 INFO: Appending 'datas' from .spec
519 INFO: checking Analysis
519 INFO: Building Analysis because Analysis-00.toc is non existent
519 INFO: Running Analysis Analysis-00.toc
519 INFO: Target bytecode optimization level: 0
519 INFO: Initializing module dependency graph...
521 INFO: Initializing module graph hook caches...
528 INFO: Analyzing modules for base_library.zip ...
1024 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
1052 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
1675 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2116 INFO: Caching module dependency graph...
2138 INFO: Looking for Python shared library...
2140 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python310.dll
2140 INFO: Analyzing D:\LocalQA\main.py
2158 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2214 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2531 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
2540 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'      
2540 INFO: SetuptoolsInfo: initializing cached setuptools info...
950 WARNING: Failed to collect submodules for 'setuptools._vendor.packaging.licenses' because importing 'set
uptools._vendor.packaging.licenses' raised: ModuleNotFoundError: No module named 'packaging.licenses'       
4391 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
4410 INFO: Processing standard module hook 'hook-torch.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
1116 WARNING: Failed to collect submodules for 'torch.utils.tensorboard' because importing 'torch.utils.tens
orboard' raised: ModuleNotFoundError: No module named 'tensorboard'
[2025-07-01 12:24:09,980] torch.distributed.elastic.multiprocessing.redirects: [WARNING] NOTE: Redirects are
 currently not supported in Windows or MacOs.
9088 INFO: hook-torch: this torch build does not depend on MKL...
9089 INFO: hook-torch: raising recursion limit to 5000
9100 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9135 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9392 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9393 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptool
s._vendor.jaraco.text'!
9395 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9410 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9410 INFO: Processing pre-find-module-path hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
9569 INFO: Processing standard module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9577 INFO: Processing standard module hook 'hook-distutils.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9592 INFO: Processing standard module hook 'hook-_osx_support.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9613 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9657 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'       
9657 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setu
ptools._vendor.jaraco.functools'!
9661 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'     
9669 INFO: Processing standard module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
9670 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
9782 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10031 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10096 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\
\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10097 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\App
Data\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'   
10098 INFO: Processing standard module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10109 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'        
10110 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setup
tools._vendor.jaraco.context'!
10113 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'     
10114 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'se
tuptools._vendor.backports.tarfile'!
10140 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10141 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
10144 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10248 INFO: Processing standard module hook 'hook-numpy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10448 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
10869 INFO: Processing standard module hook 'hook-psutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
11640 INFO: Processing standard module hook 'hook-sympy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
12019 INFO: Processing standard module hook 'hook-matplotlib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12227 INFO: Processing pre-safe-import-module hook 'hook-gi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
12323 INFO: Processing standard module hook 'hook-matplotlib.backend_bases.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12358 INFO: Processing standard module hook 'hook-PIL.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12423 INFO: Processing standard module hook 'hook-PIL.Image.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12682 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
12734 INFO: Processing standard module hook 'hook-PIL.ImageFilter.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13027 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13227 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13254 INFO: Processing standard module hook 'hook-matplotlib.pyplot.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
13670 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13762 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
14268 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14351 INFO: Processing standard module hook 'hook-py.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14637 INFO: Processing standard module hook 'hook-pytest.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14888 INFO: Processing standard module hook 'hook-pygments.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
19152 INFO: Processing standard module hook 'hook-scipy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
20464 INFO: Processing standard module hook 'hook-lxml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
20704 INFO: Processing standard module hook 'hook-lxml.etree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
20795 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
21232 INFO: Processing standard module hook 'hook-scipy.linalg.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
21503 INFO: Processing standard module hook 'hook-scipy.sparse.csgraph.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
21536 INFO: Processing standard module hook 'hook-scipy.special._ufuncs.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
21581 INFO: Processing standard module hook 'hook-scipy.special._ellip_harm_2.py' from 'C:\\Users\\<USER>\\Ap
pData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
22029 INFO: Processing standard module hook 'hook-scipy.spatial.transform.rotation.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
22586 INFO: Processing standard module hook 'hook-scipy.stats._stats.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
24425 INFO: Processing standard module hook 'hook-xml.dom.domreg.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
25395 INFO: Processing standard module hook 'hook-pandas.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
26228 INFO: Processing standard module hook 'hook-pytz.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
26846 INFO: Processing standard module hook 'hook-pandas.io.formats.style.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
27737 INFO: Processing standard module hook 'hook-pyarrow.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28215 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28587 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28621 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29286 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
29373 INFO: Processing standard module hook 'hook-bcrypt.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29413 INFO: Processing standard module hook 'hook-fsspec.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29764 INFO: Processing standard module hook 'hook-lz4.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
30263 INFO: Processing standard module hook 'hook-pandas.plotting.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
30656 INFO: Processing standard module hook 'hook-openpyxl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
31544 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
33304 INFO: Processing standard module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
33543 INFO: Processing standard module hook 'hook-pandas.io.clipboard.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
35658 INFO: Processing standard module hook 'hook-shelve.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
36532 INFO: Processing standard module hook 'hook-transformers.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
38081 INFO: Processing standard module hook 'hook-pydantic.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\experimental\__init__.py:7
: PydanticExperimentalWarning: This module is experimental, its contents are subject to change and deprecati
on.
  warnings.warn(
39073 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40094 INFO: Processing standard module hook 'hook-anyio.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40611 INFO: Processing standard module hook 'hook-orjson.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
41457 INFO: Processing standard module hook 'hook-websockets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
42327 INFO: Processing standard module hook 'hook-uvicorn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
43074 INFO: Processing standard module hook 'hook-bitsandbytes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
43168 INFO: Processing standard module hook 'hook-torchvision.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
43195 INFO: Processing standard module hook 'hook-torchvision.io.image.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
43273 INFO: Processing standard module hook 'hook-torchaudio.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
46749 INFO: Processing standard module hook 'hook-scipy.io.matlab.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
47627 INFO: Processing standard module hook 'hook-onnxruntime.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
48006 INFO: Processing pre-safe-import-module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'      
48070 INFO: Processing standard module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
48071 INFO: Processing standard module hook 'hook-pythoncom.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
48137 INFO: Processing standard module hook 'hook-pywintypes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
48891 INFO: Processing standard module hook 'hook-wcwidth.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
49541 INFO: Processing standard module hook 'hook-datasets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
49612 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
49613 INFO: TclTkInfo: initializing cached Tcl/Tk info...
49739 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
50085 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
50612 INFO: Processing standard module hook 'hook-regex.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
50659 INFO: Processing standard module hook 'hook-spacy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
54720 INFO: Processing standard module hook 'hook-thinc.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
56302 INFO: Processing standard module hook 'hook-srsly.msgpack._packer.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
56453 INFO: Processing pre-safe-import-module hook 'hook-tensorflow.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'    
56488 INFO: Processing standard module hook 'hook-thinc.backends.numpy_ops.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
56645 INFO: Processing standard module hook 'hook-langcodes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
58716 INFO: Processing standard module hook 'hook-jieba.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
58876 INFO: Processing standard module hook 'hook-astor.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
62354 INFO: Processing standard module hook 'hook-cv2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
63457 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
63474 INFO: Processing standard module hook 'hook-grpc.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
64968 INFO: Processing standard module hook 'hook-timm.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
67578 INFO: Processing standard module hook 'hook-emoji.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
72668 INFO: Processing standard module hook 'hook-nltk.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
73891 INFO: Processing standard module hook 'hook-sklearn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
74137 INFO: Processing standard module hook 'hook-sklearn.utils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
74190 INFO: Processing standard module hook 'hook-sklearn.metrics.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75266 INFO: Processing standard module hook 'hook-sklearn.metrics.cluster.py' from 'C:\\Users\\<USER>\\AppDat
a\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75316 INFO: Processing standard module hook 'hook-sklearn.cluster.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75335 INFO: Processing standard module hook 'hook-sklearn.metrics.pairwise.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75413 INFO: Processing standard module hook 'hook-sklearn.neighbors.py' from 'C:\\Users\\<USER>\\AppData\\Loc
al\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75446 INFO: Processing standard module hook 'hook-sklearn.linear_model.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
84881 INFO: Processing standard module hook 'hook-PyQt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
85403 INFO: Processing standard module hook 'hook-PyQt6.QtWidgets.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
85834 INFO: Processing standard module hook 'hook-PyQt6.QtCore.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
86097 INFO: Processing standard module hook 'hook-PyQt6.QtGui.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
87342 INFO: Processing standard module hook 'hook-opentelemetry.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90144 INFO: Processing standard module hook 'hook-docx.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90329 INFO: Processing standard module hook 'hook-pdfminer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progr
ams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
90527 INFO: Processing standard module hook 'hook-pptx.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91730 INFO: Processing standard module hook 'hook-pypdfium2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91741 INFO: Processing standard module hook 'hook-pypdfium2_raw.py' from 'C:\\Users\\<USER>\\AppData\\Local\\
Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91933 INFO: Processing standard module hook 'hook-magic.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91966 INFO: Processing standard module hook 'hook-iso639.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
91975 INFO: Processing standard module hook 'hook-langdetect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pro
grams\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\whoosh\codec\whoosh3.py:1116: Synta
xWarning: "is" with a literal. Did you mean "=="?
  elif fixedsize is 0:
94538 INFO: Processing standard module hook 'hook-PyQt6.QtWebEngineWidgets.py' from 'C:\\Users\\<USER>\\AppDa
ta\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94771 INFO: Processing standard module hook 'hook-PyQt6.QtNetwork.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
94944 INFO: Processing standard module hook 'hook-PyQt6.QtPrintSupport.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
95009 INFO: Processing standard module hook 'hook-PyQt6.QtWebChannel.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
95091 INFO: Processing standard module hook 'hook-PyQt6.QtWebEngineCore.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
95205 INFO: Processing module hooks (post-graph stage)...
95378 INFO: Processing standard module hook 'hook-lxml.isoschematron.py' from 'C:\\Users\\<USER>\\AppData\\Lo
cal\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
95627 INFO: Processing standard module hook 'hook-zope.interface.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
95665 INFO: Processing standard module hook 'hook-sklearn.tree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
99771 WARNING: Hidden import "importlib_resources.trees" not found!
99771 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\L
ocal\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
99772 INFO: Matplotlib backend selection method: automatic discovery of used backends
99777 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'sympy.testing.runtests':
 ['Agg', 'Agg']
99780 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'gradio.utils': ['agg']  
99781 INFO: Discovered Matplotlib backend(s) via `matplotlib.use()` call in module 'paddle.dataset.uci_housi
ng': ['Agg']
99782 INFO: The following Matplotlib backends were discovered by scanning for `matplotlib.use()` calls: ['Ag
g', 'agg']. If your backend of choice is not in this list, either add a `matplotlib.use()` call to your code
, or configure the backend collection via hook options (see: https://pyinstaller.org/en/stable/hooks-config.
html#matplotlib-hooks).
99782 INFO: Selected matplotlib backends: ['Agg', 'agg']
100066 INFO: Processing standard module hook 'hook-PIL.SpiderImagePlugin.py' from 'C:\\Users\\<USER>\\AppData
\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
100851 INFO: Processing standard module hook 'hook-PyQt6.QtQuick.py' from 'C:\\Users\\<USER>\\AppData\\Local\
\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101083 INFO: Processing standard module hook 'hook-PyQt6.QtQml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\P
rograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101496 INFO: Processing standard module hook 'hook-PyQt6.QtOpenGL.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101607 INFO: Processing standard module hook 'hook-PyQt6.QtPositioning.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101684 INFO: Processing standard module hook 'hook-PyQt6.QtQuickWidgets.py' from 'C:\\Users\\<USER>\\AppData\
\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
101760 WARNING: Hidden import "scipy.special._cdflib" not found!
101888 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
102106 WARNING: Hidden import "pysqlite2" not found!
102106 WARNING: Hidden import "MySQLdb" not found!
102107 WARNING: Hidden import "psycopg2" not found!
102491 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Prog
rams\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
102493 INFO: Processing standard module hook 'hook-lxml.objectify.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
102493 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Progra
ms\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
103085 INFO: Performing binary vs. data reclassification (7919 entries)
103473 INFO: Looking for ctypes DLLs
103525 WARNING: Ignoring /usr/lib64/libgomp.so.1 imported from C:\Users\<USER>\AppData\Local\Programs\Python\
Python310\lib\site-packages\torch\_inductor\codecache.py - only basenames are supported with ctypes imports!
103588 INFO: Analyzing run-time hooks ...
103623 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103625 INFO: Including run-time hook 'pyi_rth_pyqt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Py
thon\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103626 INFO: Processing pre-find-module-path hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\
Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
103627 INFO: Processing standard module hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\Local
\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'
103628 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\
Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103629 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103630 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\P
ython\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103631 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Loca
l\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103632 INFO: Including run-time hook 'pyi_rth_mplconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103636 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\
\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103636 INFO: Including run-time hook 'pyi_rth_nltk.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Pyt
hon\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103638 INFO: Including run-time hook 'pyi_rth_pywintypes.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Program
s\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103638 INFO: Including run-time hook 'pyi_rth_pythoncom.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs
\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
103639 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Pr
ograms\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
103868 INFO: Creating base_library.zip...
103915 INFO: Looking for dynamic libraries
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py:
227: UserWarning: The numpy.array_api submodule is still experimental. See NEP 47.
  __import__(package)
Could not find an up-to-date installation of `packaging`. License expressions might not be validated. To enf
orce validation, please install `packaging>=24.2`.
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py:
227: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resour
ces.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this pa
ckage or pin to Setuptools<81.
  __import__(package)
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\experimental\__init__.py:7
: PydanticExperimentalWarning: This module is experimental, its contents are subject to change and deprecati
on.
  warnings.warn(
```

---

_**User**_

你之前做了什么，接下来还需要做什么

---

_**Assistant**_



---

_**User**_

你之前做了什么，接下来还需要做什么

---

_**Assistant**_



---



---



---

