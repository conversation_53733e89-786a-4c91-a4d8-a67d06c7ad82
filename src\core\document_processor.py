"""
文档处理模块
支持PDF、Word、Excel文档的文本提取和分块处理
"""
import os
import re
import io
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import hashlib
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing

# 文档处理库
try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

try:
    from docx import Document
except ImportError:
    Document = None

try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from unstructured.partition.pdf import partition_pdf
    from unstructured.partition.docx import partition_docx
except ImportError:
    partition_pdf = None
    partition_docx = None

# OCR相关
try:
    import pytesseract
    from PIL import Image
    from pdf2image import convert_from_path
except ImportError:
    pytesseract = None
    Image = None
    convert_from_path = None

from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import (
    calculate_file_hash, 
    get_file_size_mb, 
    clean_text,
    extract_department_from_filename,
    safe_filename,
    ProgressTracker
)

logger = get_logger(__name__)


@dataclass
class DocumentChunk:
    """文档分块数据结构"""
    id: str
    content: str
    metadata: Dict[str, Any]
    source_file: str
    page_number: Optional[int] = None
    chunk_index: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'content': self.content,
            'metadata': self.metadata,
            'source_file': self.source_file,
            'page_number': self.page_number,
            'chunk_index': self.chunk_index
        }


@dataclass
class DocumentMetadata:
    """文档元数据"""
    file_path: str
    file_name: str
    file_size_mb: float
    file_hash: str
    department: str
    document_type: str
    creation_date: Optional[str] = None
    modification_date: Optional[str] = None
    page_count: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'file_path': self.file_path,
            'file_name': self.file_name,
            'file_size_mb': self.file_size_mb,
            'file_hash': self.file_hash,
            'department': self.department,
            'document_type': self.document_type,
            'creation_date': self.creation_date,
            'modification_date': self.modification_date,
            'page_count': self.page_count
        }


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self):
        self.config = get_config()
        self.supported_formats = self.config.processing.supported_formats
        self.chunk_size = self.config.processing.chunk_size
        self.chunk_overlap = self.config.processing.chunk_overlap
        
        # 初始化OCR设置
        self._setup_ocr()
    
    def _setup_ocr(self):
        """设置OCR"""
        if pytesseract is None:
            logger.warning("pytesseract未安装，OCR功能不可用")
            return

        try:
            # 尝试多个可能的Tesseract路径（Windows）
            if os.name == 'nt':
                possible_paths = [
                    r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                    r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                    r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
                    'tesseract'  # 系统PATH中
                ]

                tesseract_found = False
                for path in possible_paths:
                    if path == 'tesseract' or os.path.exists(path):
                        try:
                            pytesseract.pytesseract.tesseract_cmd = path
                            pytesseract.get_tesseract_version()
                            tesseract_found = True
                            logger.info(f"找到Tesseract: {path}")
                            break
                        except Exception:
                            continue

                if not tesseract_found:
                    logger.warning("未找到Tesseract，OCR功能不可用")
                    return
            else:
                # Linux/Mac系统，通常在PATH中
                pytesseract.get_tesseract_version()

            logger.info("OCR初始化成功")
        except Exception as e:
            logger.warning(f"OCR初始化失败: {e}")
    
    def extract_metadata(self, file_path: Path) -> DocumentMetadata:
        """提取文档元数据"""
        try:
            stat = file_path.stat()
            file_hash = calculate_file_hash(file_path)
            department = extract_department_from_filename(str(file_path))
            
            metadata = DocumentMetadata(
                file_path=str(file_path),
                file_name=file_path.name,
                file_size_mb=get_file_size_mb(file_path),
                file_hash=file_hash,
                department=department,
                document_type=file_path.suffix.lower(),
                creation_date=str(stat.st_ctime),
                modification_date=str(stat.st_mtime)
            )
            
            # 获取页数（PDF）
            if file_path.suffix.lower() == '.pdf':
                try:
                    doc = fitz.open(file_path)
                    metadata.page_count = len(doc)
                    doc.close()
                except Exception as e:
                    logger.warning(f"无法获取PDF页数 {file_path}: {e}")
            
            return metadata
            
        except Exception as e:
            logger.error(f"提取元数据失败 {file_path}: {e}")
            raise
    
    def extract_text_from_pdf(self, file_path: Path) -> List[Tuple[str, int]]:
        """从PDF提取文本"""
        texts = []
        
        try:
            # 首先尝试直接提取文本
            doc = fitz.open(file_path)
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                
                # 如果文本太少，可能是扫描版PDF，使用OCR
                if len(text.strip()) < 50:
                    try:
                        # 转换为图像并OCR
                        pix = page.get_pixmap()
                        img_data = pix.tobytes("png")
                        img = Image.open(io.BytesIO(img_data))
                        
                        ocr_text = pytesseract.image_to_string(
                            img, 
                            lang=self.config.processing.ocr_language
                        )
                        text = ocr_text if len(ocr_text) > len(text) else text
                        
                    except Exception as e:
                        logger.warning(f"OCR处理失败 {file_path} 第{page_num+1}页: {e}")
                
                if text.strip():
                    texts.append((clean_text(text), page_num + 1))
            
            doc.close()
            
        except Exception as e:
            logger.error(f"PDF文本提取失败 {file_path}: {e}")
            # 尝试使用unstructured库
            try:
                elements = partition_pdf(str(file_path))
                for i, element in enumerate(elements):
                    if hasattr(element, 'text') and element.text.strip():
                        page_num = getattr(element, 'metadata', {}).get('page_number', i + 1)
                        texts.append((clean_text(element.text), page_num))
            except Exception as e2:
                logger.error(f"备用PDF处理也失败 {file_path}: {e2}")
                raise
        
        return texts
    
    def extract_text_from_docx(self, file_path: Path) -> List[Tuple[str, int]]:
        """从Word文档提取文本"""
        texts = []
        
        try:
            # 使用python-docx
            doc = Document(file_path)
            
            full_text = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    full_text.append(paragraph.text)
            
            # 处理表格
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        full_text.append(' | '.join(row_text))
            
            if full_text:
                combined_text = '\n'.join(full_text)
                texts.append((clean_text(combined_text), 1))
            
        except Exception as e:
            logger.error(f"Word文档处理失败 {file_path}: {e}")
            # 尝试使用unstructured库
            try:
                elements = partition_docx(str(file_path))
                for element in elements:
                    if hasattr(element, 'text') and element.text.strip():
                        texts.append((clean_text(element.text), 1))
            except Exception as e2:
                logger.error(f"备用Word处理也失败 {file_path}: {e2}")
                raise
        
        return texts
    
    def extract_text_from_excel(self, file_path: Path) -> List[Tuple[str, int]]:
        """从Excel文档提取文本"""
        texts = []
        
        try:
            # 读取所有工作表
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_num, sheet_name in enumerate(excel_file.sheet_names):
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 转换为文本
                    sheet_text = []
                    
                    # 添加工作表名称
                    sheet_text.append(f"工作表: {sheet_name}")
                    
                    # 处理列名
                    if not df.empty:
                        headers = [str(col) for col in df.columns if str(col) != 'nan']
                        if headers:
                            sheet_text.append("列名: " + " | ".join(headers))
                        
                        # 处理数据行
                        for _, row in df.iterrows():
                            row_data = []
                            for value in row:
                                if pd.notna(value) and str(value).strip():
                                    row_data.append(str(value).strip())
                            if row_data:
                                sheet_text.append(" | ".join(row_data))
                    
                    if sheet_text:
                        combined_text = '\n'.join(sheet_text)
                        texts.append((clean_text(combined_text), sheet_num + 1))
                        
                except Exception as e:
                    logger.warning(f"处理工作表 {sheet_name} 失败: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Excel文档处理失败 {file_path}: {e}")
            raise
        
        return texts
    
    def extract_text(self, file_path: Path) -> List[Tuple[str, int]]:
        """提取文档文本"""
        file_ext = file_path.suffix.lower()
        
        if file_ext == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_ext in ['.docx', '.doc']:
            return self.extract_text_from_docx(file_path)
        elif file_ext in ['.xlsx', '.xls']:
            return self.extract_text_from_excel(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
    
    def create_chunks(self, texts: List[Tuple[str, int]], 
                     metadata: DocumentMetadata) -> List[DocumentChunk]:
        """创建文档分块"""
        chunks = []
        
        for text, page_num in texts:
            if not text.strip():
                continue
            
            # 简单的文本分块策略
            text_chunks = self._split_text(text, self.chunk_size, self.chunk_overlap)
            
            for i, chunk_text in enumerate(text_chunks):
                if len(chunk_text.strip()) < 20:  # 跳过太短的分块
                    continue
                
                chunk_id = self._generate_chunk_id(metadata.file_hash, page_num, i)
                
                chunk_metadata = metadata.to_dict()
                chunk_metadata.update({
                    'page_number': page_num,
                    'chunk_index': i,
                    'chunk_length': len(chunk_text)
                })
                
                chunk = DocumentChunk(
                    id=chunk_id,
                    content=chunk_text,
                    metadata=chunk_metadata,
                    source_file=metadata.file_path,
                    page_number=page_num,
                    chunk_index=i
                )
                
                chunks.append(chunk)
        
        return chunks
    
    def _split_text(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """分割文本"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 尝试在句号、感叹号、问号处分割
            if end < len(text):
                for i in range(end, max(start + chunk_size // 2, start + 1), -1):
                    if text[i] in '。！？\n':
                        end = i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap if end < len(text) else end
            
            if start >= len(text):
                break
        
        return chunks
    
    def _generate_chunk_id(self, file_hash: str, page_num: int, chunk_index: int) -> str:
        """生成分块ID"""
        content = f"{file_hash}_{page_num}_{chunk_index}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def process_document(self, file_path: Path) -> List[DocumentChunk]:
        """处理单个文档"""
        logger.info(f"开始处理文档: {file_path}")
        
        try:
            # 检查文件格式
            if file_path.suffix.lower() not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
            
            # 提取元数据
            metadata = self.extract_metadata(file_path)
            
            # 提取文本
            texts = self.extract_text(file_path)
            
            if not texts:
                logger.warning(f"文档无有效文本: {file_path}")
                return []
            
            # 创建分块
            chunks = self.create_chunks(texts, metadata)
            
            logger.info(f"文档处理完成: {file_path}, 生成 {len(chunks)} 个分块")
            return chunks
            
        except Exception as e:
            logger.error(f"文档处理失败 {file_path}: {e}")
            return []
    
    def process_documents(self, docs_dir: Path, use_parallel: bool = True, max_workers: Optional[int] = None) -> List[DocumentChunk]:
        """批量处理文档"""
        logger.info(f"开始批量处理文档目录: {docs_dir}")

        # 扫描文档文件
        from ..utils.helpers import scan_documents
        documents = scan_documents(docs_dir, self.supported_formats)

        if not documents:
            logger.warning("未找到支持的文档文件")
            return []

        if use_parallel and len(documents) > 1:
            return self._process_documents_parallel(documents, max_workers)
        else:
            return self._process_documents_sequential(documents)

    def _process_documents_sequential(self, documents: List[Path]) -> List[DocumentChunk]:
        """顺序处理文档"""
        all_chunks = []
        progress = ProgressTracker(len(documents), "文档处理")

        for doc_path in documents:
            try:
                chunks = self.process_document(doc_path)
                all_chunks.extend(chunks)
                progress.update()

            except Exception as e:
                logger.error(f"处理文档失败 {doc_path}: {e}")
                progress.update()
                continue

        progress.finish()
        logger.info(f"顺序处理完成，总共生成 {len(all_chunks)} 个分块")
        return all_chunks

    def _process_documents_parallel(self, documents: List[Path], max_workers: Optional[int] = None) -> List[DocumentChunk]:
        """并行处理文档"""
        if max_workers is None:
            max_workers = min(4, multiprocessing.cpu_count())  # 限制最大线程数

        logger.info(f"使用 {max_workers} 个线程并行处理文档")

        all_chunks = []
        progress = ProgressTracker(len(documents), "并行文档处理")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_doc = {
                executor.submit(self.process_document, doc_path): doc_path
                for doc_path in documents
            }

            # 收集结果
            for future in as_completed(future_to_doc):
                doc_path = future_to_doc[future]
                try:
                    chunks = future.result()
                    all_chunks.extend(chunks)
                    progress.update()

                except Exception as e:
                    logger.error(f"并行处理文档失败 {doc_path}: {e}")
                    progress.update()
                    continue

        progress.finish()
        logger.info(f"并行处理完成，总共生成 {len(all_chunks)} 个分块")
        return all_chunks
