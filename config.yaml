# 公司制度查询平台配置文件

# 模型配置
model:
  embedding_model: "shibing624/text2vec-base-chinese"
  llm_model: "qwen-1.5-1.8b-chat"
  max_tokens: 512   # 适中的token数量
  temperature: 0.7  # 平衡创造性和准确性
  device: "auto"    # auto, cpu, cuda

# 数据库配置
database:
  chroma_persist_dir: "data/chroma_db"
  whoosh_index_dir: "data/whoosh_index"
  collection_name: "policy_documents"

# 文档处理配置
processing:
  chunk_size: 512
  chunk_overlap: 50
  supported_formats:
    - ".pdf"
    - ".docx"
    - ".doc"
    - ".xlsx"
    - ".xls"
  ocr_language: "chi_sim+eng"

# 界面配置
ui:
  window_title: "公司制度查询平台"
  window_width: 1200
  window_height: 800
  theme: "light"

# 路径配置
docs_dir: "docs"
data_dir: "data"
models_dir: "models"
preview_dir: "data/preview_pdfs"

# 性能配置
max_memory_gb: 8
enable_cache: true
cache_size: 1000

# 日志配置
log_level: "INFO"
log_file: "logs/app.log"
