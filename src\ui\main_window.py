"""
主窗口模块
"""
import sys
import os
from pathlib import Path
from typing import Optional

try:
    from PyQt6.QtWidgets import (
        QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QTabWidget, QStatusBar, QMenuBar, QToolBar,
        QLabel, QPushButton, QMessageBox, QSplitter,
        QTextEdit, QProgressBar, QFrame, QStackedWidget,
        QTreeWidget, QTreeWidgetItem, QInputDialog, QMenu,
        QComboBox
    )
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize
    from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap, QPalette, QColor
except ImportError as e:
    raise e

from ..utils.config import get_config
from ..utils.logger import get_logger
from ..core.search_system import get_search_system

logger = get_logger(__name__)


class DeviceSwitchThread(QThread):
    """设备切换线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, device_type: str):
        super().__init__()
        self.device_type = device_type
        self.should_stop = False

    def run(self):
        """执行设备切换"""
        try:
            from ..core.device_manager import get_device_manager
            from ..core.ai_model import get_ai_model

            device_manager = get_device_manager()
            ai_model = get_ai_model()

            # 步骤1: 卸载当前模型
            self.status_updated.emit("正在卸载当前模型...")
            self.progress_updated.emit(20)

            if ai_model.model_loaded:
                ai_model.unload_model()

            if self.should_stop:
                return

            # 步骤2: 切换设备
            self.status_updated.emit("正在切换设备...")
            self.progress_updated.emit(40)

            success = device_manager.switch_device(self.device_type)
            if not success:
                self.error_occurred.emit("设备切换失败")
                return

            if self.should_stop:
                return

            # 步骤3: 更新AI模型设备
            self.status_updated.emit("正在更新模型配置...")
            self.progress_updated.emit(60)

            ai_model.device = device_manager.current_device

            # 步骤4: 重新加载模型
            self.status_updated.emit("正在重新加载模型...")
            self.progress_updated.emit(80)

            ai_model.load_model(force_reload=True)

            # 完成
            self.progress_updated.emit(100)
            self.status_updated.emit("设备切换完成")

        except Exception as e:
            self.error_occurred.emit(f"设备切换失败: {str(e)}")

    def stop(self):
        """停止切换"""
        self.should_stop = True


class StatusUpdateThread(QThread):
    """状态更新线程"""
    status_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.running = True
    
    def run(self):
        """运行状态更新"""
        while self.running:
            try:
                search_system = get_search_system()
                stats = search_system.get_system_stats()
                self.status_updated.emit(stats)
            except Exception as e:
                logger.error(f"状态更新失败: {e}")
            
            self.msleep(5000)  # 5秒更新一次
    
    def stop(self):
        """停止线程"""
        self.running = False


class MainWindow(QMainWindow):
    """风格主窗口类"""

    def __init__(self):
        super().__init__()
        self.config = get_config()
        self.search_system = None
        self.status_thread = None

        # 子组件
        self.qa_interface = None
        self.search_interface = None
        self.preview_widget = None
        self.current_page = 0  # 0: 智能问答, 1: 制度检索

        # 初始化界面
        self._init_ui()
        self._apply_cnki_style()
        self._init_search_system()
        self._start_status_updates()
    
    def _init_ui(self):
        """初始化风格用户界面"""
        try:
            # 设置窗口属性
            self.setWindowTitle("公司制度查询平台")
            self.setMinimumSize(1280, 720)
            self.setGeometry(100, 100, 1400, 800)

            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)

            # 创建主布局
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)

            # 创建顶部导航栏
            self._create_top_navigation(main_layout)

            # 创建主内容区域
            content_layout = QHBoxLayout()
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(0)

            # 创建左侧功能区
            self._create_left_sidebar(content_layout)

            # 创建中央内容区
            self._create_central_content(content_layout)

            main_layout.addLayout(content_layout)

            # 创建状态栏
            self._create_status_bar()

            logger.info("风格主界面初始化完成")

        except Exception as e:
            logger.error(f"界面初始化失败: {e}")
            self._show_error("界面初始化失败", str(e))
    
    def _create_top_navigation(self, parent_layout):
        """创建顶部导航栏"""
        try:
            nav_frame = QFrame()
            nav_frame.setFixedHeight(60)
            nav_frame.setStyleSheet("""
                QFrame {
                    background-color: #1a5099;
                    border: none;
                }
            """)

            nav_layout = QHBoxLayout(nav_frame)
            nav_layout.setContentsMargins(20, 0, 20, 0)

            # 系统标题
            title_label = QLabel("公司制度查询平台")
            title_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 18pt;
                    font-weight: bold;
                    background: transparent;
                }
            """)
            nav_layout.addWidget(title_label)

            nav_layout.addStretch()

            # 设备切换控件
            self._create_device_switcher(nav_layout)

            parent_layout.addWidget(nav_frame)

        except Exception as e:
            logger.error(f"顶部导航栏创建失败: {e}")

    def _create_device_switcher(self, nav_layout):
        """创建设备切换控件"""
        try:
            from ..core.device_manager import get_device_manager

            self.device_manager = get_device_manager()
            device_info = self.device_manager.get_device_info()

            # 设备状态标签
            self.device_status_label = QLabel()
            self.device_status_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 10pt;
                    background: transparent;
                    padding: 5px 10px;
                }
            """)

            # 设备切换下拉框
            self.device_combo = QComboBox()
            self.device_combo.addItem("🔄 自动选择", "auto")
            self.device_combo.addItem("🖥️ CPU模式", "cpu")

            if device_info['gpu_available']:
                gpu_text = f"🚀 GPU模式 ({device_info['gpu_memory_gb']:.1f}GB)"
                self.device_combo.addItem(gpu_text, "cuda")

            self.device_combo.setStyleSheet("""
                QComboBox {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 4px;
                    padding: 5px 10px;
                    font-size: 10pt;
                    min-width: 120px;
                }
                QComboBox:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }
                QComboBox::drop-down {
                    border: none;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid white;
                    margin-right: 5px;
                }
                QComboBox QAbstractItemView {
                    background-color: white;
                    color: black;
                    selection-background-color: #1a5099;
                    border: 1px solid #ccc;
                }
            """)

            self.device_combo.currentTextChanged.connect(self._on_device_changed)

            # 添加到布局
            nav_layout.addWidget(self.device_status_label)
            nav_layout.addWidget(self.device_combo)

            # 初始化设备状态
            self._update_device_status()

        except Exception as e:
            logger.error(f"设备切换控件创建失败: {e}")

    def _create_left_sidebar(self, parent_layout):
        """创建左侧功能区"""
        try:
            sidebar_frame = QFrame()
            sidebar_frame.setFixedWidth(140) 
            sidebar_frame.setStyleSheet("""
                QFrame {
                    background-color: #f0f4f9;
                    border-right: 1px solid #e0e6ed;
                }
            """)

            sidebar_layout = QVBoxLayout(sidebar_frame)
            sidebar_layout.setContentsMargins(0, 20, 0, 0)
            sidebar_layout.setSpacing(5)

            # 智能问答按钮
            self.qa_btn = QPushButton("🤖 智能问答")
            self.qa_btn.setFixedHeight(50)
            self.qa_btn.clicked.connect(lambda: self._switch_page(0))

            # 制度检索按钮
            self.search_btn = QPushButton("🔍 制度检索")
            self.search_btn.setFixedHeight(50)
            self.search_btn.clicked.connect(lambda: self._switch_page(1))

            # 设置按钮样式
            try:
                from .cnki_styles import SIDEBAR_BUTTON_STYLE
                self.qa_btn.setStyleSheet(SIDEBAR_BUTTON_STYLE)
                self.search_btn.setStyleSheet(SIDEBAR_BUTTON_STYLE)
            except ImportError:
                # 备用样式
                button_style = """
                    QPushButton {
                        text-align: left;
                        padding: 12px 20px;
                        border: none;
                        background: transparent;
                        font-size: 11pt;
                        color: #333;
                    }
                    QPushButton:hover {
                        background-color: #e6f0ff;
                    }
                    QPushButton:checked {
                        background-color: #1a5099;
                        color: white;
                        border-left: 4px solid #0d3d73;
                        font-weight: bold;
                    }
                """
                self.qa_btn.setStyleSheet(button_style)
                self.search_btn.setStyleSheet(button_style)
            self.qa_btn.setCheckable(True)
            self.search_btn.setCheckable(True)
            self.qa_btn.setChecked(True)  # 默认选中智能问答

            # 创建导航树
            self._create_navigation_tree(sidebar_layout)

            parent_layout.addWidget(sidebar_frame)

        except Exception as e:
            logger.error(f"左侧功能区创建失败: {e}")

    def _create_navigation_tree(self, parent_layout):
        """创建导航树"""
        try:
            # 创建树形控件
            self.nav_tree = QTreeWidget()
            self.nav_tree.setHeaderHidden(True)
            self.nav_tree.setRootIsDecorated(True)
            # 设置缩进为0，避免隔断
            self.nav_tree.setIndentation(0)
            self.nav_tree.setStyleSheet("""
                QTreeWidget {
                    border: none;
                    background-color: transparent;
                    font-size: 11pt;
                    outline: none;
                }
                QTreeWidget::item {
                    padding: 8px;
                    border: none;
                    border-radius: 4px;
                    margin: 1px;
                }
                QTreeWidget::item:hover {
                    background-color: rgba(26, 80, 153, 0.1);
                }
                QTreeWidget::item:selected {
                    background-color: #1a5099;
                    color: white;
                    font-weight: bold;
                }
                QTreeWidget::item:selected:active {
                    background-color: #1a5099;
                    color: white;
                }
                QTreeWidget::item:selected:!active {
                    background-color: #1a5099;
                    color: white;
                }
                /* 移除所有分支图标 */
                QTreeWidget::branch:has-children:!has-siblings:closed,
                QTreeWidget::branch:closed:has-children:has-siblings,
                QTreeWidget::branch:open:has-children:!has-siblings,
                QTreeWidget::branch:open:has-children:has-siblings {
                    border-image: none;
                    image: url(none);
                }
                /* 移除子项的分支图标，避免高亮时的隔断 */
                QTreeWidget::branch:has-children:!has-siblings:closed:has-children,
                QTreeWidget::branch:closed:has-children:has-siblings:has-children,
                QTreeWidget::branch:open:has-children:!has-siblings:has-children,
                QTreeWidget::branch:open:has-children:has-siblings:has-children {
                    border-image: none;
                    image: url(none);
                }
                /* 确保子项没有缩进 */
                QTreeWidget::item:has-children {
                    padding-left: 8px;
                }
                QTreeWidget::item:!has-children {
                    padding-left: 8px;
                }
            """)

            # 智能问答根节点
            self.qa_root = QTreeWidgetItem(self.nav_tree)
            self.qa_root.setText(0, "🤖 智能问答")
            self.qa_root.setData(0, Qt.ItemDataRole.UserRole, "qa_root")
            self.qa_root.setExpanded(True)

            # 新建对话项
            self.new_chat_item = QTreeWidgetItem(self.qa_root)
            self.new_chat_item.setText(0, "➕ 新建对话")
            self.new_chat_item.setData(0, Qt.ItemDataRole.UserRole, "new_chat")
            # 为新建对话项设置更小的字体
            font = QFont()
            font.setPointSize(10)  # 比默认的11pt小一号
            self.new_chat_item.setFont(0, font)

            # 制度检索根节点
            self.search_root = QTreeWidgetItem(self.nav_tree)
            self.search_root.setText(0, "🔍 制度检索")
            self.search_root.setData(0, Qt.ItemDataRole.UserRole, "search_root")

            # 连接点击事件
            self.nav_tree.itemClicked.connect(self._on_nav_item_clicked)
            self.nav_tree.itemDoubleClicked.connect(self._on_nav_item_double_clicked)

            # 添加右键菜单
            self.nav_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            self.nav_tree.customContextMenuRequested.connect(self._show_nav_context_menu)

            parent_layout.addWidget(self.nav_tree)

            # 初始化会话历史
            self._refresh_session_tree()

            # 默认选中新建对话
            self.nav_tree.setCurrentItem(self.new_chat_item)

        except Exception as e:
            logger.error(f"导航树创建失败: {e}")

    def _on_nav_item_clicked(self, item, column):
        """导航项点击事件"""
        try:
            item_type = item.data(0, Qt.ItemDataRole.UserRole)
            logger.info(f"导航项点击: {item.text(0)}, 类型: {item_type}")

            if item_type == "qa_root":
                # 点击智能问答根节点，展开/折叠
                item.setExpanded(not item.isExpanded())

            elif item_type == "new_chat":
                # 点击新建对话 - 总是创建新会话
                logger.info("创建新会话")
                self._create_new_session()
                self._switch_page(0)
                # 确保新会话被选中和高亮
                self._refresh_session_tree()

            elif item_type == "search_root":
                # 点击制度检索
                logger.info("切换到制度检索")
                self._switch_page(1)

            elif item_type and item_type.startswith("session_"):
                # 点击历史会话 - 直接切换到该会话
                session_id = item_type.replace("session_", "")
                logger.info(f"点击历史会话 - 原始数据: {item_type}, 提取的会话ID: {session_id}")

                # 先切换到会话，再设置高亮
                self._switch_to_session(session_id)
                self._switch_page(0)
            else:
                logger.warning(f"未知的导航项类型: {item_type}")

        except Exception as e:
            logger.error(f"导航项点击处理失败: {e}")

    def _on_nav_item_double_clicked(self, item, column):
        """导航项双击事件"""
        try:
            item_type = item.data(0, Qt.ItemDataRole.UserRole)

            if item_type and item_type.startswith("session_"):
                # 双击历史会话，显示操作菜单
                session_id = item_type.replace("session_", "")
                self._show_session_action_dialog(session_id)

        except Exception as e:
            logger.error(f"导航项双击处理失败: {e}")

    def _show_session_action_dialog(self, session_id: str):
        """显示会话操作对话框"""
        try:
            from ..core.session_manager import get_session_manager
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

            session_manager = get_session_manager()
            session = session_manager.load_session(session_id)
            if not session:
                return

            dialog = QDialog(self)
            dialog.setWindowTitle("会话操作")
            dialog.setFixedSize(300, 150)

            layout = QVBoxLayout(dialog)

            # 会话信息
            info_label = QLabel(f"会话: {session.title}")
            info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
            layout.addWidget(info_label)

            # 按钮区域
            button_layout = QHBoxLayout()

            # 重命名按钮
            rename_btn = QPushButton("重命名")
            rename_btn.clicked.connect(lambda: (dialog.accept(), self._rename_session(session_id)))
            button_layout.addWidget(rename_btn)

            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet("background-color: #dc3545; color: white;")
            delete_btn.clicked.connect(lambda: (dialog.accept(), self._delete_session(session_id)))
            button_layout.addWidget(delete_btn)

            # 取消按钮
            cancel_btn = QPushButton("取消")
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            logger.error(f"显示会话操作对话框失败: {e}")

    def _refresh_session_tree(self):
        """刷新会话树"""
        try:
            from ..core.session_manager import get_session_manager

            # 清除现有的会话项
            for i in range(self.qa_root.childCount() - 1, 0, -1):  # 保留"新建对话"项
                self.qa_root.removeChild(self.qa_root.child(i))

            session_manager = get_session_manager()
            sessions = session_manager.list_sessions()
            current_session = session_manager.get_current_session()

            # 记录当前需要高亮的会话项
            current_session_item = None

            # 显示所有会话（包括当前会话和空会话）
            if current_session:
                # 获取所有会话，包括当前会话（即使没有消息）
                historical_sessions = []
                for session in sessions:
                    # 显示当前会话（即使没有消息）或有消息的会话
                    if session.id == current_session.id or len(session.messages) > 0:
                        historical_sessions.append(session)
                
                # 添加历史会话项
                for session in historical_sessions[:10]:  # 最多显示10个会话
                    session_item = QTreeWidgetItem(self.qa_root)

                    # 设置会话项不显示分支图标
                    session_item.setChildIndicatorPolicy(QTreeWidgetItem.ChildIndicatorPolicy.DontShowIndicator)

                    # 设置会话项没有缩进
                    session_item.setSizeHint(0, session_item.sizeHint(0))

                    # 显示完整标题，不截断
                    session_item.setText(0, f"💬 {session.title}")
                    session_data = f"session_{session.id}"
                    session_item.setData(0, Qt.ItemDataRole.UserRole, session_data)
                    session_item.setToolTip(0, f"{session.title}\n右键菜单可重命名或删除")  # 完整标题作为提示

                    # 设置历史会话项的字体大小（比默认小一号）
                    font = session_item.font(0)
                    font.setPointSize(10)  # 从11pt减少到10pt
                    session_item.setFont(0, font)

                    logger.info(f"添加会话项: {session.title}, ID: {session.id}, 数据: {session_data}")

                    # 标记当前会话项
                    if current_session and session.id == current_session.id:
                        current_session_item = session_item
                        logger.info(f"标记当前会话项: {session.title}")

            # 确保智能问答节点展开
            self.qa_root.setExpanded(True)

            # 设置当前会话的高亮（在所有项创建完成后）
            if current_session_item:
                # 使用粗体字体，但保持较小的字号
                font = current_session_item.font(0)
                font.setBold(True)
                font.setPointSize(10)  # 确保保持10pt字号
                current_session_item.setFont(0, font)

                # 设置为当前选中项（蓝色高亮）
                self.nav_tree.setCurrentItem(current_session_item)
                logger.info(f"当前会话高亮设置完成: {current_session_item.text(0)}")

        except Exception as e:
            logger.error(f"刷新会话树失败: {e}")

    def _switch_to_session(self, session_id: str):
        """切换到指定会话"""
        try:
            logger.info(f"开始切换到会话: {session_id}")
            from ..core.session_manager import get_session_manager

            session_manager = get_session_manager()
            success = session_manager.switch_session(session_id)
            logger.info(f"会话切换结果: {success}")

            if success:
                # 刷新会话树（这会自动设置当前会话的高亮）
                logger.info("刷新会话树")
                self._refresh_session_tree()

                # 确保高亮设置正确
                self._ensure_current_session_highlighted(session_id)

                # 如果当前在问答页面，刷新界面
                if self.current_page == 0 and hasattr(self.qa_interface, 'refresh_session_display'):
                    logger.info("刷新问答界面")
                    self.qa_interface.refresh_session_display()
                else:
                    logger.info(f"当前页面: {self.current_page}, 问答界面存在: {hasattr(self.qa_interface, 'refresh_session_display')}")

                logger.info(f"会话切换成功: {session_id}")
            else:
                logger.warning(f"会话切换失败: {session_id}")

        except Exception as e:
            logger.error(f"切换会话失败: {e}")

    def _ensure_current_session_highlighted(self, session_id: str):
        """确保当前会话正确高亮"""
        try:
            # 遍历所有会话项，找到对应的项并设置高亮
            for i in range(self.qa_root.childCount()):
                item = self.qa_root.child(i)
                item_type = item.data(0, Qt.ItemDataRole.UserRole)
                
                if item_type and item_type.startswith("session_"):
                    item_session_id = item_type.replace("session_", "")
                    
                    if item_session_id == session_id:
                        # 找到目标会话项，设置高亮
                        font = item.font(0)
                        font.setBold(True)
                        font.setPointSize(10)  # 确保保持10pt字号
                        item.setFont(0, font)

                        # 设置为当前选中项
                        self.nav_tree.setCurrentItem(item)
                        logger.info(f"确保高亮设置: {item.text(0)}")
                        break
                        
        except Exception as e:
            logger.error(f"确保当前会话高亮失败: {e}")

    def _show_nav_context_menu(self, position):
        """显示导航树右键菜单"""
        try:
            item = self.nav_tree.itemAt(position)
            if not item:
                return

            item_type = item.data(0, Qt.ItemDataRole.UserRole)

            # 只为会话项显示右键菜单
            if item_type and item_type.startswith("session_"):
                session_id = item_type.replace("session_", "")
                menu = QMenu(self)

                # 重命名
                rename_action = QAction("重命名", self)
                rename_action.triggered.connect(lambda: self._rename_session(session_id))
                menu.addAction(rename_action)

                # 删除
                delete_action = QAction("删除", self)
                delete_action.triggered.connect(lambda: self._delete_session(session_id))
                menu.addAction(delete_action)

                menu.exec(self.nav_tree.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示导航菜单失败: {e}")

    def _create_central_content(self, parent_layout):
        """创建中央内容区"""
        try:
            # 创建堆叠窗口部件
            self.stacked_widget = QStackedWidget()

            # 导入并创建界面组件
            from .enhanced_qa_interface import EnhancedQAInterface
            from .cnki_search_interface import CNKISearchInterface

            self.qa_interface = EnhancedQAInterface()
            self.search_interface = CNKISearchInterface()

            # 添加到堆叠窗口
            self.stacked_widget.addWidget(self.qa_interface)
            self.stacked_widget.addWidget(self.search_interface)

            # 连接信号
            if hasattr(self.qa_interface, 'preview_requested'):
                self.qa_interface.preview_requested.connect(self._on_preview_requested)
            if hasattr(self.search_interface, 'preview_requested'):
                self.search_interface.preview_requested.connect(self._on_preview_requested)

            parent_layout.addWidget(self.stacked_widget)

        except ImportError as e:
            logger.error(f"界面组件导入失败: {e}")
            # 创建占位符
            placeholder = QLabel("界面组件加载中...")
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setStyleSheet("font-size: 14pt; color: #666;")
            parent_layout.addWidget(placeholder)
    
    def _switch_page(self, page_index):
        """切换页面"""
        try:
            self.current_page = page_index
            self.stacked_widget.setCurrentIndex(page_index)

            # 更新导航树选择状态
            if page_index == 0:
                # 智能问答页面，检查是否有当前会话，如果有则保持选中，否则选中新建对话项
                from ..core.session_manager import get_session_manager
                session_manager = get_session_manager()
                current_session = session_manager.get_current_session()
                
                if current_session and hasattr(self, 'qa_root'):
                    # 查找当前会话项并选中
                    for i in range(self.qa_root.childCount()):
                        item = self.qa_root.child(i)
                        item_type = item.data(0, Qt.ItemDataRole.UserRole)
                        if item_type and item_type.startswith("session_"):
                            item_session_id = item_type.replace("session_", "")
                            if item_session_id == current_session.id:
                                self.nav_tree.setCurrentItem(item)
                                break
                    else:
                        # 如果没找到当前会话项，选中新建对话项
                        if hasattr(self, 'new_chat_item'):
                            self.nav_tree.setCurrentItem(self.new_chat_item)
                else:
                    # 没有当前会话，选中新建对话项
                    if hasattr(self, 'new_chat_item'):
                        self.nav_tree.setCurrentItem(self.new_chat_item)
            else:
                # 制度检索页面，选中制度检索根节点
                if hasattr(self, 'search_root'):
                    self.nav_tree.setCurrentItem(self.search_root)

            # 更新状态栏
            page_names = ["智能问答", "制度检索"]
            self._update_status(f"当前页面: {page_names[page_index]}")

        except Exception as e:
            logger.error(f"页面切换失败: {e}")

    def _create_new_session(self):
        """创建新会话"""
        try:
            from ..core.session_manager import get_session_manager

            session_manager = get_session_manager()
            session = session_manager.create_new_session()

            # 刷新会话树
            self._refresh_session_tree()

            # 立即选中新创建的会话项
            if hasattr(self, 'qa_root'):
                for i in range(self.qa_root.childCount()):
                    item = self.qa_root.child(i)
                    item_type = item.data(0, Qt.ItemDataRole.UserRole)
                    if item_type and item_type.startswith("session_"):
                        item_session_id = item_type.replace("session_", "")
                        if item_session_id == session.id:
                            self.nav_tree.setCurrentItem(item)
                            break

            # 如果当前在问答页面，刷新界面
            if self.current_page == 0 and hasattr(self.qa_interface, 'refresh_session_display'):
                self.qa_interface.refresh_session_display()

        except Exception as e:
            logger.error(f"创建新会话失败: {e}")

    def _rename_session(self, session_id: str):
        """重命名会话"""
        try:
            from ..core.session_manager import get_session_manager

            session_manager = get_session_manager()
            session = session_manager.load_session(session_id)
            if not session:
                return

            new_title, ok = QInputDialog.getText(
                self, "重命名会话", "请输入新的会话名称:",
                text=session.title
            )

            if ok and new_title.strip():
                session.title = new_title.strip()
                session_manager._save_session(session)
                self._refresh_session_tree()

        except Exception as e:
            logger.error(f"重命名会话失败: {e}")

    def _delete_session(self, session_id: str):
        """删除会话"""
        try:
            from ..core.session_manager import get_session_manager

            reply = QMessageBox.question(
                self, "确认删除", "确定要删除这个会话吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                session_manager = get_session_manager()
                success = session_manager.delete_session(session_id)

                if success:
                    self._refresh_session_tree()

                    # 如果当前在问答页面，刷新界面
                    if self.current_page == 0 and hasattr(self.qa_interface, 'refresh_session_display'):
                        self.qa_interface.refresh_session_display()

        except Exception as e:
            logger.error(f"删除会话失败: {e}")

    def _apply_cnki_style(self):
        """应用风格样式"""
        try:
            from .cnki_styles import apply_cnki_style
            apply_cnki_style(self)

        except ImportError as e:
            logger.error(f"样式模块导入失败: {e}")
        except Exception as e:
            logger.error(f"样式应用失败: {e}")
    
    def _create_menu_bar(self):
        """创建菜单栏（风格简化版）"""
        try:
            menubar = self.menuBar()
            menubar.setStyleSheet("""
                QMenuBar {
                    background-color: #f8fafc;
                    border-bottom: 1px solid #e0e6ed;
                    padding: 2px;
                }
                QMenuBar::item {
                    background-color: transparent;
                    padding: 4px 8px;
                    margin: 2px;
                }
                QMenuBar::item:selected {
                    background-color: #e6f0ff;
                    border-radius: 4px;
                }
            """)

            # 文件菜单
            file_menu = menubar.addMenu('📁 文件')

            # 导入文档动作
            import_action = QAction('📥 导入文档', self)
            import_action.triggered.connect(self._on_import_documents)
            file_menu.addAction(import_action)

            file_menu.addSeparator()

            # 退出动作
            exit_action = QAction('🚪 退出', self)
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)

            # 工具菜单
            tools_menu = menubar.addMenu('🔧 工具')

            # 重建索引动作
            rebuild_action = QAction('🔄 重建索引', self)
            rebuild_action.triggered.connect(self._on_rebuild_index)
            tools_menu.addAction(rebuild_action)

            # 清空缓存动作
            clear_cache_action = QAction('🗑️ 清空缓存', self)
            clear_cache_action.triggered.connect(self._on_clear_cache)
            tools_menu.addAction(clear_cache_action)

            # 帮助菜单
            help_menu = menubar.addMenu('❓ 帮助')

            # 关于动作
            about_action = QAction('ℹ️ 关于', self)
            about_action.triggered.connect(self._on_about)
            help_menu.addAction(about_action)

        except Exception as e:
            logger.error(f"菜单栏创建失败: {e}")
    
    def _create_status_bar(self):
        """创建风格状态栏"""
        try:
            self.status_bar = self.statusBar()
            self.status_bar.setStyleSheet("""
                QStatusBar {
                    background-color: #f8fafc;
                    border-top: 1px solid #e0e6ed;
                    color: #666;
                    font-size: 9pt;
                }
                QStatusBar::item {
                    border: none;
                }
            """)

            # 状态标签
            self.status_label = QLabel("🟢 系统就绪")
            self.status_label.setStyleSheet("color: #52c41a; font-weight: bold;")
            self.status_bar.addWidget(self.status_label)

            # 进度条
            self.progress_bar = QProgressBar()
            self.progress_bar.setVisible(False)
            self.progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #d9d9d9;
                    border-radius: 3px;
                    text-align: center;
                    height: 16px;
                }
                QProgressBar::chunk {
                    background-color: #1a5099;
                    border-radius: 2px;
                }
            """)
            self.status_bar.addPermanentWidget(self.progress_bar)

            # 统计信息标签
            self.stats_label = QLabel("📊 统计信息加载中...")
            self.stats_label.setStyleSheet("color: #666;")
            self.status_bar.addPermanentWidget(self.stats_label)

        except Exception as e:
            logger.error(f"状态栏创建失败: {e}")
    
    def _init_search_system(self):
        """初始化搜索系统"""
        try:
            self.search_system = get_search_system()
            self._update_status("搜索系统已就绪")
            
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
            self._update_status(f"搜索系统初始化失败: {str(e)}")
    
    def _start_status_updates(self):
        """开始状态更新"""
        try:
            self.status_thread = StatusUpdateThread()
            self.status_thread.status_updated.connect(self._on_status_updated)
            self.status_thread.start()
            
        except Exception as e:
            logger.error(f"状态更新启动失败: {e}")
    
    def _on_status_updated(self, stats: dict):
        """处理状态更新"""
        try:
            vector_stats = stats.get('vector_store', {})
            index_stats = stats.get('text_index', {})
            
            total_chunks = vector_stats.get('total_chunks', 0)
            total_docs = index_stats.get('total_documents', 0)
            cache_size = stats.get('cache_size', 0)
            
            stats_text = f"文档: {total_docs} | 分块: {total_chunks} | 缓存: {cache_size}"
            self.stats_label.setText(stats_text)
            
        except Exception as e:
            logger.error(f"状态更新处理失败: {e}")
    
    def _on_preview_requested(self, file_path: str, page: int = 1):
        """处理预览请求"""
        try:
            from .cnki_preview_dialog import CNKIPreviewDialog

            # 创建并显示预览对话框
            preview_dialog = CNKIPreviewDialog(file_path, page, self)
            preview_dialog.exec()

        except ImportError as e:
            logger.error(f"预览对话框导入失败: {e}")
            self._show_error("预览失败", "预览组件加载失败")
        except Exception as e:
            logger.error(f"文档预览失败: {e}")
            self._show_error("预览失败", str(e))
    
    def _update_status(self, message: str):
        """更新状态"""
        try:
            self.status_label.setText(message)
            logger.info(f"状态更新: {message}")
            
        except Exception as e:
            logger.error(f"状态更新失败: {e}")
    
    def _show_error(self, title: str, message: str):
        """显示错误对话框"""
        try:
            QMessageBox.critical(self, title, message)
        except Exception as e:
            logger.error(f"错误对话框显示失败: {e}")
    
    def _show_info(self, title: str, message: str):
        """显示信息对话框"""
        try:
            QMessageBox.information(self, title, message)
        except Exception as e:
            logger.error(f"信息对话框显示失败: {e}")

    # 设备切换相关方法
    def _on_device_changed(self):
        """设备切换处理"""
        try:
            selected_device = self.device_combo.currentData()
            if selected_device:
                self._switch_device(selected_device)
        except Exception as e:
            logger.error(f"设备切换处理失败: {e}")

    def _switch_device(self, device_type: str):
        """切换设备"""
        try:
            # 显示切换进度
            self._show_device_switching_dialog(device_type)

        except Exception as e:
            logger.error(f"设备切换失败: {e}")
            self._show_error("设备切换失败", str(e))

    def _show_device_switching_dialog(self, device_type: str):
        """显示设备切换对话框"""
        try:
            from PyQt6.QtWidgets import QProgressDialog
            from PyQt6.QtCore import QTimer

            # 创建进度对话框
            progress = QProgressDialog("正在切换设备...", "取消", 0, 100, self)
            progress.setWindowTitle("设备切换")
            progress.setModal(True)
            progress.setMinimumDuration(0)
            progress.show()

            # 创建切换线程
            self.switch_thread = DeviceSwitchThread(device_type)
            self.switch_thread.progress_updated.connect(progress.setValue)
            self.switch_thread.status_updated.connect(progress.setLabelText)
            self.switch_thread.finished.connect(lambda: self._on_device_switch_finished(progress))
            self.switch_thread.error_occurred.connect(lambda msg: self._on_device_switch_error(progress, msg))

            progress.canceled.connect(self.switch_thread.terminate)
            self.switch_thread.start()

        except Exception as e:
            logger.error(f"设备切换对话框创建失败: {e}")

    def _on_device_switch_finished(self, progress_dialog):
        """设备切换完成"""
        try:
            progress_dialog.close()
            self._update_device_status()
            self._show_info("设备切换", "设备切换完成！")
        except Exception as e:
            logger.error(f"设备切换完成处理失败: {e}")

    def _on_device_switch_error(self, progress_dialog, error_msg):
        """设备切换错误"""
        try:
            progress_dialog.close()
            self._show_error("设备切换失败", error_msg)
        except Exception as e:
            logger.error(f"设备切换错误处理失败: {e}")

    def _update_device_status(self):
        """更新设备状态显示"""
        try:
            if hasattr(self, 'device_manager'):
                status = self.device_manager.get_device_status()
                current_device = status.get('current_device', 'auto')
                performance_mode = status.get('performance_mode', '自动')

                if current_device == 'cpu':
                    status_text = f"💻 {performance_mode}"
                    status_color = "#ff9800"  # 橙色
                elif current_device == 'cuda':
                    status_text = f"🚀 {performance_mode}"
                    status_color = "#4caf50"  # 绿色
                else:
                    status_text = "🔄 自动模式"
                    status_color = "#2196f3"  # 蓝色

                if hasattr(self, 'device_status_label'):
                    self.device_status_label.setText(status_text)
                    self.device_status_label.setStyleSheet(f"""
                        QLabel {{
                            color: {status_color};
                            font-size: 10pt;
                            font-weight: bold;
                            background: transparent;
                            padding: 5px 10px;
                        }}
                    """)
        except Exception as e:
            logger.error(f"设备状态更新失败: {e}")

    # 菜单和工具栏事件处理
    def _on_import_documents(self):
        """导入文档"""
        self._show_info("导入文档", "请使用预处理脚本导入文档")
    
    def _on_rebuild_index(self):
        """重建索引"""
        self._show_info("重建索引", "请使用预处理脚本重建索引")
    
    def _on_clear_cache(self):
        """清空缓存"""
        try:
            if self.search_system:
                self.search_system.clear_cache()
                self._show_info("清空缓存", "缓存已清空")
        except Exception as e:
            self._show_error("清空缓存失败", str(e))
    
    def _on_refresh(self):
        """刷新"""
        self._update_status("刷新中...")
        # 这里可以添加刷新逻辑
        self._update_status("就绪")
    
    def _on_settings(self):
        """设置"""
        self._show_info("设置", "设置功能开发中...")
    
    def _on_about(self):
        """关于"""
        about_text = """
🏢 公司制度智能平台

📋 版本: 1.0.0
🎯 基于AI的公司制度文档查询平台

🛠️ 技术栈:
• PyQt6 - 现代化界面框架
• ChromaDB - 向量数据库
• Whoosh - 全文搜索引擎
• ChatGLM3-6B - AI语言模型

🎨 界面设计: 学术风格
🤖 开发: Augment Agent

💡 功能特色:
• 智能问答 - AI理解自然语言问题
• 制度检索 - 快速精准搜索文档
• 文档预览 - 在线查看PDF文件
• 来源引用 - 学术级引用格式
        """
        self._show_info("关于系统", about_text.strip())
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止状态更新线程
            if self.status_thread:
                self.status_thread.stop()
                self.status_thread.wait(3000)  # 等待3秒
            
            logger.info("应用程序正在关闭...")
            event.accept()
            
        except Exception as e:
            logger.error(f"关闭事件处理失败: {e}")
            event.accept()
