"""
CNKI风格样式定义
"""

# CNKI主题色彩
CNKI_COLORS = {
    'primary': '#1a5099',      # 主色调：CNKI学术蓝
    'primary_dark': '#0d3d73', # 深蓝色
    'primary_light': '#e6f0ff', # 浅蓝色
    'secondary': '#f0f4f9',    # 辅助色
    'background': '#ffffff',   # 背景白色
    'border': '#e0e6ed',      # 边框色
    'text': '#333333',        # 主文本色
    'text_light': '#666666',  # 浅文本色
    'text_lighter': '#999999', # 更浅文本色
    'success': '#52c41a',     # 成功色
    'warning': '#faad14',     # 警告色
    'error': '#ff4d4f',       # 错误色
    'card_bg': '#fafafa',     # 卡片背景色
}

# 全局样式表
GLOBAL_STYLESHEET = f"""
/* 全局样式 */
QWidget {{
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
    font-size: 10pt;
    color: {CNKI_COLORS['text']};
}}

/* 主窗口背景 */
QMainWindow {{
    background-color: {CNKI_COLORS['background']};
}}

/* 卡片样式 */
.card {{
    background-color: {CNKI_COLORS['background']};
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 8px;
    padding: 15px;
}}

.card:hover {{
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}}

/* 按钮样式 */
.primary-button {{
    background-color: {CNKI_COLORS['primary']};
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
}}

.primary-button:hover {{
    background-color: {CNKI_COLORS['primary_dark']};
}}

.primary-button:disabled {{
    background-color: #ccc;
    color: #999;
}}

.secondary-button {{
    background-color: #f0f0f0;
    color: {CNKI_COLORS['text']};
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 10pt;
}}

.secondary-button:hover {{
    background-color: #e0e0e0;
}}

/* 输入框样式 */
.input-field {{
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: white;
    font-size: 10pt;
}}

.input-field:focus {{
    border-color: {CNKI_COLORS['primary']};
    outline: none;
}}

/* 表格样式 */
QTableWidget {{
    gridline-color: {CNKI_COLORS['border']};
    alternate-background-color: #f8fafc;
    background-color: white;
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 6px;
    selection-background-color: {CNKI_COLORS['primary_light']};
}}

QHeaderView::section {{
    background-color: {CNKI_COLORS['primary']};
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
    font-size: 10pt;
}}

QTableWidget::item {{
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}}

QTableWidget::item:selected {{
    background-color: {CNKI_COLORS['primary_light']};
    color: {CNKI_COLORS['primary']};
}}

/* 列表样式 */
QListWidget {{
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 6px;
    background-color: white;
}}

QListWidget::item {{
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}}

QListWidget::item:hover {{
    background-color: {CNKI_COLORS['primary_light']};
}}

QListWidget::item:selected {{
    background-color: {CNKI_COLORS['primary']};
    color: white;
}}

/* 文本编辑器样式 */
QTextEdit, QTextBrowser {{
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 6px;
    padding: 10px;
    background-color: white;
    font-size: 10pt;
    line-height: 1.6;
}}

QTextEdit:focus, QTextBrowser:focus {{
    border-color: {CNKI_COLORS['primary']};
}}

/* 滚动条样式 */
QScrollBar:vertical {{
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}}

QScrollBar::handle:vertical {{
    background-color: #c0c0c0;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}}

QScrollBar::handle:vertical:hover {{
    background-color: #a0a0a0;
}}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
    height: 0;
}}

QScrollBar:horizontal {{
    background-color: #f0f0f0;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}}

QScrollBar::handle:horizontal {{
    background-color: #c0c0c0;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}}

QScrollBar::handle:horizontal:hover {{
    background-color: #a0a0a0;
}}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
    width: 0;
}}

/* 分组框样式 */
QGroupBox {{
    font-weight: bold;
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 6px;
    margin-top: 10px;
    padding-top: 10px;
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: {CNKI_COLORS['primary']};
}}

/* 标签页样式 */
QTabWidget::pane {{
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 6px;
    background-color: white;
}}

QTabBar::tab {{
    background-color: #f0f4f9;
    border: 1px solid {CNKI_COLORS['border']};
    border-bottom: none;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    padding: 8px 16px;
    margin-right: 2px;
}}

QTabBar::tab:selected {{
    background-color: white;
    border-bottom: 2px solid {CNKI_COLORS['primary']};
    color: {CNKI_COLORS['primary']};
    font-weight: bold;
}}

QTabBar::tab:hover:!selected {{
    background-color: {CNKI_COLORS['primary_light']};
}}

/* 进度条样式 */
QProgressBar {{
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    text-align: center;
    height: 16px;
    background-color: #f0f0f0;
}}

QProgressBar::chunk {{
    background-color: {CNKI_COLORS['primary']};
    border-radius: 2px;
}}

/* 旋转框样式 */
QSpinBox {{
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10pt;
    background-color: white;
}}

QSpinBox:focus {{
    border-color: {CNKI_COLORS['primary']};
}}

/* 复选框和单选框样式 */
QCheckBox, QRadioButton {{
    spacing: 8px;
    font-size: 10pt;
}}

QCheckBox::indicator, QRadioButton::indicator {{
    width: 16px;
    height: 16px;
}}

QCheckBox::indicator:unchecked {{
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background-color: white;
}}

QCheckBox::indicator:checked {{
    border: 1px solid {CNKI_COLORS['primary']};
    border-radius: 3px;
    background-color: {CNKI_COLORS['primary']};
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
}}

/* 工具提示样式 */
QToolTip {{
    background-color: {CNKI_COLORS['text']};
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 9pt;
}}
"""

# 特定组件样式
SIDEBAR_BUTTON_STYLE = f"""
QPushButton {{
    text-align: left;
    padding: 12px 20px;
    border: none;
    background: transparent;
    font-size: 11pt;
    color: {CNKI_COLORS['text']};
    border-radius: 0;
}}

QPushButton:hover {{
    background-color: {CNKI_COLORS['primary_light']};
}}

QPushButton:checked {{
    background-color: {CNKI_COLORS['primary']};
    color: white;
    border-left: 4px solid {CNKI_COLORS['primary_dark']};
    font-weight: bold;
}}
"""

TOP_NAV_STYLE = f"""
QFrame {{
    background-color: {CNKI_COLORS['primary']};
    border: none;
}}

QLabel {{
    color: white;
    font-size: 18pt;
    font-weight: bold;
    background: transparent;
}}

QPushButton {{
    color: white;
    background: transparent;
    border: 1px solid white;
    border-radius: 15px;
    padding: 5px 15px;
    font-size: 10pt;
}}

QPushButton:hover {{
    background-color: rgba(255, 255, 255, 0.1);
}}
"""

CARD_STYLE = f"""
QFrame {{
    background-color: white;
    border: 1px solid {CNKI_COLORS['border']};
    border-radius: 8px;
    padding: 15px;
}}

QFrame:hover {{
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}}
"""

def apply_cnki_style(widget):
    """应用CNKI风格到指定组件"""
    widget.setStyleSheet(GLOBAL_STYLESHEET)

def get_color(color_name: str) -> str:
    """获取CNKI主题色"""
    return CNKI_COLORS.get(color_name, '#000000')
