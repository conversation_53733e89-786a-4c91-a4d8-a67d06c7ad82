2025-07-01 12:37:04 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:37:04 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 12:37:04 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:37:04 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 12:37:04 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': False, 'psutil_available': True}
2025-07-01 12:37:04 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | memory_available_gb: 22.0
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 12:37:04 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✗ torch_available: False
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 12:37:04 | INFO     | __main__:main:98 | 检测到打包环境，跳过依赖检查
2025-07-01 12:37:04 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 12:37:04 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 12:37:04 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 12:37:04 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\dist\data
2025-07-01 12:37:04 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\dist\models
2025-07-01 12:37:04 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\dist\docs
2025-07-01 12:37:04 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 12:37:10 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 12:37:10 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 12:37:10 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 12:37:10 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 新对话
2025-07-01 12:37:10 | INFO     | src.core.session_manager:_load_or_create_default_session:96 | 创建默认会话
2025-07-01 12:37:10 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 新对话, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:10 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 新对话
2025-07-01 12:37:10 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 新对话
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 12:37:11 | WARNING  | src.core.vector_store:__init__:59 | SentenceTransformers不可用，向量搜索功能将受限
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 12:37:11 | INFO     | src.core.text_index:_initialize_index:126 | 创建新索引: D:\LocalQA\dist\data\whoosh_index
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 12:37:11 | WARNING  | src.core.ai_model:__init__:46 | Torch不可用，AI模型功能将受限
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 12:37:11 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 12:37:11 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 12:37:11 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 12:37:15 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 12:37:15 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 12:37:15 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
