2025-07-01 12:37:04 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:37:04 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 12:37:04 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:37:04 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 12:37:04 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': False, 'psutil_available': True}
2025-07-01 12:37:04 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | memory_available_gb: 22.0
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 12:37:04 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 12:37:04 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✗ torch_available: False
2025-07-01 12:37:04 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 12:37:04 | INFO     | __main__:main:98 | 检测到打包环境，跳过依赖检查
2025-07-01 12:37:04 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 12:37:04 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 12:37:04 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 12:37:04 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\dist\data
2025-07-01 12:37:04 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\dist\models
2025-07-01 12:37:04 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\dist\docs
2025-07-01 12:37:04 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 12:37:10 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 12:37:10 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 12:37:10 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 12:37:10 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 新对话
2025-07-01 12:37:10 | INFO     | src.core.session_manager:_load_or_create_default_session:96 | 创建默认会话
2025-07-01 12:37:10 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 新对话, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:10 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 新对话
2025-07-01 12:37:10 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 新对话
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 12:37:11 | WARNING  | src.core.vector_store:__init__:59 | SentenceTransformers不可用，向量搜索功能将受限
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 12:37:11 | INFO     | src.core.text_index:_initialize_index:126 | 创建新索引: D:\LocalQA\dist\data\whoosh_index
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 12:37:11 | WARNING  | src.core.ai_model:__init__:46 | Torch不可用，AI模型功能将受限
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 12:37:11 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 12:37:11 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 12:37:11 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 12:37:11 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 12:37:11 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 12:37:15 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 12:37:15 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 12:37:15 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 12:37:25 | WARNING  | src.core.vector_store:search:213 | 向量存储或嵌入模型不可用，跳过语义搜索
2025-07-01 12:37:25 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:37:25 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '业务招待费', 返回 0 个结果
2025-07-01 12:37:25 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:37:25 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 新对话, 类型: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d, 提取的会话ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: 0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:28 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 新对话
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 新对话, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 新对话
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 新对话
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 新对话
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: 0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:37:28 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 12:37:31 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 12:37:31 | WARNING  | src.core.vector_store:search:213 | 向量存储或嵌入模型不可用，跳过语义搜索
2025-07-01 12:37:31 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:37:33 | INFO     | src.core.device_manager:_apply_cpu_optimization:107 | 应用CPU性能优化...
2025-07-01 12:37:33 | INFO     | src.core.device_manager:_apply_cpu_optimization:158 | CPU优化完成: 使用8个线程
2025-07-01 12:37:33 | INFO     | src.core.device_manager:switch_device:98 | 设备已切换到: cpu
2025-07-01 12:37:33 | WARNING  | src.core.ai_model:load_model:134 | Torch不可用，使用模拟模型
2025-07-01 12:37:37 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 12:37:37 | INFO     | src.core.search_system:vector_search:221 | 从缓存返回向量搜索结果: 业务招待费
2025-07-01 12:37:38 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 12:37:38 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 12:37:38 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 12:39:00 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 12:39:01 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
2025-07-01 12:43:54 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:43:54 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 12:43:54 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:43:54 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 12:43:54 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': False, 'psutil_available': True}
2025-07-01 12:43:54 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | memory_available_gb: 21.93
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 12:43:54 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 12:43:54 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 12:43:54 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 12:43:54 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 12:43:54 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 12:43:54 | INFO     | __main__:check_environment:63 | ✗ torch_available: False
2025-07-01 12:43:54 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 12:43:54 | INFO     | __main__:main:98 | 检测到打包环境，跳过依赖检查
2025-07-01 12:43:54 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 12:43:54 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 12:43:54 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 12:43:54 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 12:43:56 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 12:43:56 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 12:43:57 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 12:43:57 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 业务招待费
2025-07-01 12:43:57 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:43:57 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费
2025-07-01 12:43:57 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费
2025-07-01 12:43:58 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 12:43:58 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 12:43:58 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 12:43:58 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 12:43:58 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 12:43:58 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 12:43:58 | WARNING  | src.core.vector_store:__init__:59 | SentenceTransformers不可用，向量搜索功能将受限
2025-07-01 12:43:58 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 12:43:58 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\dist\data\whoosh_index
2025-07-01 12:43:58 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 12:43:58 | WARNING  | src.core.ai_model:__init__:46 | Torch不可用，AI模型功能将受限
2025-07-01 12:43:58 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 12:43:58 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 12:43:58 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 12:43:58 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 12:43:58 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 12:43:58 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 12:44:04 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 12:44:04 | WARNING  | src.core.vector_store:search:213 | 向量存储或嵌入模型不可用，跳过语义搜索
2025-07-01 12:44:04 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:44:05 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 12:44:05 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 12:44:05 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 业务招待费, 类型: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d, 提取的会话ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: 0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:06 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 业务招待费
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 业务招待费
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: 0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 12:44:06 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 12:44:08 | WARNING  | src.core.vector_store:search:213 | 向量存储或嵌入模型不可用，跳过语义搜索
2025-07-01 12:44:08 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:44:08 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '业务招待费', 返回 0 个结果
2025-07-01 12:44:08 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:44:08 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:44:13 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 12:44:14 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
2025-07-01 12:44:22 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:44:22 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 12:44:22 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:44:22 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 12:44:22 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': False, 'psutil_available': True}
2025-07-01 12:44:22 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | memory_available_gb: 21.99
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 12:44:22 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 12:44:22 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 12:44:22 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 12:44:22 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 12:44:22 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 12:44:22 | INFO     | __main__:check_environment:63 | ✗ torch_available: False
2025-07-01 12:44:22 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 12:44:22 | INFO     | __main__:main:98 | 检测到打包环境，跳过依赖检查
2025-07-01 12:44:22 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 12:44:22 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 12:44:22 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 12:44:22 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 12:44:23 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 12:44:23 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 12:44:23 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 12:44:23 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 业务招待费
2025-07-01 12:44:23 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:23 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费
2025-07-01 12:44:23 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费
2025-07-01 12:44:23 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 12:44:23 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 12:44:23 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 12:44:23 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 12:44:23 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 12:44:23 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 12:44:23 | WARNING  | src.core.vector_store:__init__:59 | SentenceTransformers不可用，向量搜索功能将受限
2025-07-01 12:44:23 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 12:44:23 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\dist\data\whoosh_index
2025-07-01 12:44:23 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 12:44:23 | WARNING  | src.core.ai_model:__init__:46 | Torch不可用，AI模型功能将受限
2025-07-01 12:44:23 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 12:44:23 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 12:44:23 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 12:44:23 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 12:44:23 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 12:44:23 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 12:44:25 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 12:44:25 | WARNING  | src.core.vector_store:search:213 | 向量存储或嵌入模型不可用，跳过语义搜索
2025-07-01 12:44:25 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 12:44:42 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:44:42 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 12:44:42 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 12:44:42 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 12:44:42 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': False, 'psutil_available': True}
2025-07-01 12:44:42 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | memory_available_gb: 22.03
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 12:44:42 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 12:44:42 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 12:44:42 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 12:44:42 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 12:44:42 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 12:44:42 | INFO     | __main__:check_environment:63 | ✗ torch_available: False
2025-07-01 12:44:42 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 12:44:42 | INFO     | __main__:main:98 | 检测到打包环境，跳过依赖检查
2025-07-01 12:44:42 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 12:44:42 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 12:44:42 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 12:44:42 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 12:44:42 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 12:44:42 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 12:44:43 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 12:44:43 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 业务招待费
2025-07-01 12:44:43 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费, ID: 0548c65a-ca5b-4db0-983f-1beecf703e1d, 数据: session_0548c65a-ca5b-4db0-983f-1beecf703e1d
2025-07-01 12:44:43 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费
2025-07-01 12:44:43 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费
2025-07-01 12:44:43 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 12:44:43 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 12:44:43 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 12:44:43 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 12:44:43 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 12:44:43 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 12:44:43 | WARNING  | src.core.vector_store:__init__:59 | SentenceTransformers不可用，向量搜索功能将受限
2025-07-01 12:44:43 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 12:44:43 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\dist\data\whoosh_index
2025-07-01 12:44:43 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 12:44:43 | WARNING  | src.core.ai_model:__init__:46 | Torch不可用，AI模型功能将受限
2025-07-01 12:44:43 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 12:44:43 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 12:44:43 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 12:44:43 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 12:44:43 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 12:44:43 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 12:44:45 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 12:44:45 | WARNING  | src.core.vector_store:search:213 | 向量存储或嵌入模型不可用，跳过语义搜索
2025-07-01 12:44:45 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
